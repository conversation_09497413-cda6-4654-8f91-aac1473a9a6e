import { EventEmitter, ChangeDetectorRef } from '@angular/core';
import { ControlValueAccessor } from '@angular/forms';
import * as i0 from "@angular/core";
export type TextareaVariant = 'default' | 'primary' | 'success' | 'error' | 'warning' | 'info';
export type TextareaSize = 'sm' | 'md' | 'lg';
export declare class AvaTextareaComponent implements ControlValueAccessor {
    private cdr;
    label: string;
    placeholder: string;
    variant: TextareaVariant;
    size: TextareaSize;
    disabled: boolean;
    readonly: boolean;
    error: string;
    helper: string;
    rows: number;
    id: string;
    name: string;
    maxlength?: number;
    minlength?: number;
    required: boolean;
    fullWidth: boolean;
    style?: Record<string, string>;
    resizable: boolean;
    textareaBlur: EventEmitter<Event>;
    textareaFocus: EventEmitter<Event>;
    textareaInput: EventEmitter<Event>;
    textareaChange: EventEmitter<Event>;
    iconStartClick: EventEmitter<Event>;
    iconEndClick: EventEmitter<Event>;
    value: string;
    isFocused: boolean;
    constructor(cdr: ChangeDetectorRef);
    writeValue(value: string): void;
    onChange: (value: string) => void;
    onTouched: () => void;
    registerOnChange(fn: (value: string) => void): void;
    registerOnTouched(fn: () => void): void;
    setDisabledState(isDisabled: boolean): void;
    onInput(event: Event): void;
    onFocus(event: Event): void;
    onBlur(event: Event): void;
    onChange_(event: Event): void;
    onIconStartClick(event: Event): void;
    onIconEndClick(event: Event): void;
    onIconKeydown(event: KeyboardEvent, position: 'start' | 'end'): void;
    get hasError(): boolean;
    get hasHelper(): boolean;
    get inputId(): string;
    get errorId(): string;
    get helperId(): string;
    get ariaDescribedBy(): string;
    get inputClasses(): string;
    get wrapperClasses(): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<AvaTextareaComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<AvaTextareaComponent, "ava-textarea", never, { "label": { "alias": "label"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "variant": { "alias": "variant"; "required": false; }; "size": { "alias": "size"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; "error": { "alias": "error"; "required": false; }; "helper": { "alias": "helper"; "required": false; }; "rows": { "alias": "rows"; "required": false; }; "id": { "alias": "id"; "required": false; }; "name": { "alias": "name"; "required": false; }; "maxlength": { "alias": "maxlength"; "required": false; }; "minlength": { "alias": "minlength"; "required": false; }; "required": { "alias": "required"; "required": false; }; "fullWidth": { "alias": "fullWidth"; "required": false; }; "style": { "alias": "style"; "required": false; }; "resizable": { "alias": "resizable"; "required": false; }; }, { "textareaBlur": "textareaBlur"; "textareaFocus": "textareaFocus"; "textareaInput": "textareaInput"; "textareaChange": "textareaChange"; "iconStartClick": "iconStartClick"; "iconEndClick": "iconEndClick"; }, never, ["[slot=icon-start]", "[slot=icon-end]"], true, never>;
}
