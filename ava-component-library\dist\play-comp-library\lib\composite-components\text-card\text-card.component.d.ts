import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class TextCardComponent {
    iconName: string;
    title: string;
    value: string | number;
    description: string;
    width: number;
    type: 'default' | 'create' | 'prompt';
    iconColor: string;
    userCount: number;
    promptName: string;
    name: string;
    date: string;
    iconList: any;
    cardClick: EventEmitter<void>;
    iconClick: EventEmitter<void>;
    onCardClick(): void;
    iconClicked(icon: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TextCardComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TextCardComponent, "ava-text-card", never, { "iconName": { "alias": "iconName"; "required": false; }; "title": { "alias": "title"; "required": false; }; "value": { "alias": "value"; "required": false; }; "description": { "alias": "description"; "required": false; }; "width": { "alias": "width"; "required": false; }; "type": { "alias": "type"; "required": false; }; "iconColor": { "alias": "iconColor"; "required": false; }; "userCount": { "alias": "userCount"; "required": false; }; "promptName": { "alias": "promptName"; "required": false; }; "name": { "alias": "name"; "required": false; }; "date": { "alias": "date"; "required": false; }; "iconList": { "alias": "iconList"; "required": false; }; }, { "cardClick": "cardClick"; "iconClick": "iconClick"; }, never, never, true, never>;
}
