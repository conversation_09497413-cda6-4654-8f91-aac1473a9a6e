import { BadgeState, BadgeSize } from '../badges/badges.component';
import * as i0 from "@angular/core";
export type AvatarSize = 'small' | 'medium' | 'large';
export type AvatarShape = 'pill' | 'square';
export type AvatarTextType = 'status' | 'profile';
export declare class AvatarsComponent {
    size: AvatarSize;
    shape: AvatarShape;
    imageUrl: string;
    statusText?: string;
    profileText?: string;
    badgeState?: BadgeState;
    badgeSize?: BadgeSize;
    badgeCount?: number;
    get avatarClasses(): string;
    get hasBadge(): boolean;
    get hasStatusText(): boolean;
    get hasProfileText(): boolean;
    get hasAnyText(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<AvatarsComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<AvatarsComponent, "ava-avatars", never, { "size": { "alias": "size"; "required": false; }; "shape": { "alias": "shape"; "required": false; }; "imageUrl": { "alias": "imageUrl"; "required": false; }; "statusText": { "alias": "statusText"; "required": false; }; "profileText": { "alias": "profileText"; "required": false; }; "badgeState": { "alias": "badgeState"; "required": false; }; "badgeSize": { "alias": "badgeSize"; "required": false; }; "badgeCount": { "alias": "badgeCount"; "required": false; }; }, {}, never, never, true, never>;
}
