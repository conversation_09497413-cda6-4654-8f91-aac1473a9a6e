import * as i0 from "@angular/core";
export declare class FeatureCardComponent {
    heading: string;
    content: string;
    variant: 'blue' | 'red' | 'purple' | 'green' | 'orange' | 'teal';
    static ɵfac: i0.ɵɵFactoryDeclaration<FeatureCardComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<FeatureCardComponent, "ava-feature-card", never, { "heading": { "alias": "heading"; "required": false; }; "content": { "alias": "content"; "required": false; }; "variant": { "alias": "variant"; "required": false; }; }, {}, never, ["div[header]", "div[content]", "div[footer]"], true, never>;
}
