import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class CheckboxComponent {
    variant: 'default' | 'with-bg' | 'animated';
    size: 'small' | 'medium' | 'large';
    label: string;
    isChecked: boolean;
    indeterminate: boolean;
    disable: boolean;
    isCheckedChange: EventEmitter<boolean>;
    isAnimating: boolean;
    isUnchecking: boolean;
    get containerClasses(): Record<string, boolean>;
    get checkboxClasses(): Record<string, boolean>;
    get showIcon(): boolean;
    get showCheckmark(): boolean;
    toggleCheckbox(): void;
    onKeyDown(event: KeyboardEvent): void;
    private handleChecking;
    private handleUnchecking;
    private handleWithBgUnchecking;
    static ɵfac: i0.ɵɵFactoryDeclaration<CheckboxComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<CheckboxComponent, "ava-checkbox", never, { "variant": { "alias": "variant"; "required": false; }; "size": { "alias": "size"; "required": false; }; "label": { "alias": "label"; "required": false; }; "isChecked": { "alias": "isChecked"; "required": false; }; "indeterminate": { "alias": "indeterminate"; "required": false; }; "disable": { "alias": "disable"; "required": false; }; }, { "isCheckedChange": "isCheckedChange"; }, never, never, true, never>;
}
