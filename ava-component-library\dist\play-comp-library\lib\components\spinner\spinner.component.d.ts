import * as i0 from "@angular/core";
export type SpinnerType = 'circular' | 'dotted' | 'partial' | 'gradient' | 'dashed' | 'double';
export type SpinnerSize = 'sm' | 'md' | 'lg' | 'xl';
export declare class SpinnerComponent {
    type: SpinnerType;
    size: SpinnerSize;
    className: string;
    animation: boolean;
    color: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'purple';
    progressIndex?: number;
    get sizeClass(): string;
    get isStandardSpinner(): boolean;
    get progressClass(): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<SpinnerComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SpinnerComponent, "ava-spinner", never, { "type": { "alias": "type"; "required": false; }; "size": { "alias": "size"; "required": false; }; "className": { "alias": "className"; "required": false; }; "animation": { "alias": "animation"; "required": false; }; "color": { "alias": "color"; "required": false; }; "progressIndex": { "alias": "progressIndex"; "required": false; }; }, {}, never, never, true, never>;
}
