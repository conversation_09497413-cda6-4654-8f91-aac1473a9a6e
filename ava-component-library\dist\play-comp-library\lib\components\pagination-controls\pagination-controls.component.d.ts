import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class PaginationControlsComponent {
    currentPage: number;
    totalPages: number;
    type: 'basic' | 'extended' | 'standard' | 'pageinfo' | 'simplepageinfo';
    pageChange: EventEmitter<number>;
    goToPage(page: number | string): void;
    get pages(): (number | string)[];
    private getBasicPages;
    private getExtendedPages;
    private getStandardPages;
    shouldShow(page: number | string): boolean;
    shouldInsertDots(index: number): boolean;
    nextPage(): void;
    prevPage(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<PaginationControlsComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<PaginationControlsComponent, "ava-pagination-controls", never, { "currentPage": { "alias": "currentPage"; "required": false; }; "totalPages": { "alias": "totalPages"; "required": false; }; "type": { "alias": "type"; "required": false; }; }, { "pageChange": "pageChange"; }, never, never, true, never>;
}
