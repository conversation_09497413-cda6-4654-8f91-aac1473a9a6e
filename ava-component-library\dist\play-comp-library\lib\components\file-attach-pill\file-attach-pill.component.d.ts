import { EventEmitter, ElementRef } from '@angular/core';
import * as i0 from "@angular/core";
export interface FileAttachOption {
    name: string;
    icon: string;
    value: string;
    useCustomIcon?: boolean;
}
export declare class FileAttachPillComponent {
    private elementRef;
    options: FileAttachOption[];
    mainIcon: string;
    useCustomMainIcon: boolean;
    mainText: string;
    currentTheme: 'light' | 'dark';
    iconSize: number;
    optionSelected: EventEmitter<FileAttachOption>;
    isHovered: import("@angular/core").WritableSignal<boolean>;
    isDropdownOpen: import("@angular/core").WritableSignal<boolean>;
    private isMouseOverDropdown;
    constructor(elementRef: ElementRef);
    onDocumentClick(event: MouseEvent): void;
    onMouseEnter(): void;
    onMouseLeave(): void;
    onDropdownMouseEnter(): void;
    onDropdownMouseLeave(): void;
    toggleDropdown(event: Event): void;
    selectOption(option: FileAttachOption, event: Event): void;
    private closeDropdown;
    get iconColor(): string;
    trackByOptionValue(index: number, option: FileAttachOption): string;
    isCustomIcon(option?: FileAttachOption): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<FileAttachPillComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<FileAttachPillComponent, "ava-file-attach-pill", never, { "options": { "alias": "options"; "required": false; }; "mainIcon": { "alias": "mainIcon"; "required": false; }; "useCustomMainIcon": { "alias": "useCustomMainIcon"; "required": false; }; "mainText": { "alias": "mainText"; "required": false; }; "currentTheme": { "alias": "currentTheme"; "required": false; }; "iconSize": { "alias": "iconSize"; "required": false; }; }, { "optionSelected": "optionSelected"; }, never, never, true, never>;
}
