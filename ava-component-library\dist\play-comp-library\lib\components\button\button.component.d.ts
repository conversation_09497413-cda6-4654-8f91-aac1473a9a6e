import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export type ButtonVariant = 'primary' | 'secondary';
export type ButtonSize = 'small' | 'medium' | 'large' | 'normal';
export type ButtonState = 'default' | 'active' | 'disabled' | 'danger' | 'warning';
export declare class ButtonComponent {
    label: string;
    variant: ButtonVariant;
    size: ButtonSize;
    state: ButtonState;
    visual: 'normal' | 'glass' | 'neo';
    pill: boolean;
    disabled: boolean;
    width?: string;
    height?: string;
    gradient?: string;
    background?: string;
    color?: string;
    dropdown: boolean;
    iconName: string;
    iconColor: string;
    iconSize: number;
    iconPosition: 'left' | 'right' | 'only';
    userClick: EventEmitter<Event>;
    isActive: boolean;
    timeoutRef: any;
    basicOrAdvanced: string;
    ngOnInit(): void;
    handleClick(event: Event): void;
    onKeydown(event: KeyboardEvent): void;
    setActiveState(): void;
    get hasIcon(): boolean;
    get computedIconColor(): string;
    isValidColor(value: string): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ButtonComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ButtonComponent, "ava-button", never, { "label": { "alias": "label"; "required": false; }; "variant": { "alias": "variant"; "required": false; }; "size": { "alias": "size"; "required": false; }; "state": { "alias": "state"; "required": false; }; "visual": { "alias": "visual"; "required": false; }; "pill": { "alias": "pill"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "width": { "alias": "width"; "required": false; }; "height": { "alias": "height"; "required": false; }; "gradient": { "alias": "gradient"; "required": false; }; "background": { "alias": "background"; "required": false; }; "color": { "alias": "color"; "required": false; }; "dropdown": { "alias": "dropdown"; "required": false; }; "iconName": { "alias": "iconName"; "required": false; }; "iconColor": { "alias": "iconColor"; "required": false; }; "iconSize": { "alias": "iconSize"; "required": false; }; "iconPosition": { "alias": "iconPosition"; "required": false; }; }, { "userClick": "userClick"; }, never, never, true, never>;
}
