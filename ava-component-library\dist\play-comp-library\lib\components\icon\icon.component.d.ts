import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class IconComponent {
    iconName: string;
    color: string;
    disabled: boolean;
    iconColor: string;
    iconSize: number | string;
    cursor: boolean;
    userClick: EventEmitter<Event>;
    get computedColor(): string;
    handleClick(event: Event): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IconComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IconComponent, "ava-icon", never, { "iconName": { "alias": "iconName"; "required": false; }; "color": { "alias": "color"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "iconColor": { "alias": "iconColor"; "required": false; }; "iconSize": { "alias": "iconSize"; "required": false; }; "cursor": { "alias": "cursor"; "required": false; }; }, { "userClick": "userClick"; }, never, never, true, never>;
}
