/* Component: Textbox
 * Purpose: Design tokens for Textbox component
 */
 :root {
  /* Base */
  --color-border-hover: var(--color-border-default);
  
  --textbox-gap: var(--global-spacing-2);
  --textbox-gap-sm: var(--global-spacing-1);
  --textbox-gap-lg: var(--global-spacing-3);

  /* Label */
  --textbox-label-font: var(--font-label);
  --textbox-label-color: var(--color-text-primary);
  --textbox-label-margin: var(--global-spacing-1);
  --textbox-label-weight: 500;
  --textbox-required-color: var(--color-text-error);

  /* Container */
  --textbox-background: var(--app-surface);
  --textbox-border-color: var(--color-border-default);
  --textbox-border-radius: var(--global-radius-sm);
  --textbox-border-hover-color: var(--app-surface);
  --textbox-border-focus-color: var(--color-border-focus);
  --textbox-focus-shadow: var(--app-light);

  /* States */
  --textbox-background-disabled: var(--app-surface);
  --textbox-border-disabled-color: var(--color-border-disabled);
  --textbox-background-readonly: var(--color-background-subtle);
  --textbox-border-readonly-color: var(--color-border-subtle);

  /* Variants */
  --textbox-border-primary-color: var(--color-border-primary);
  --textbox-border-success-color: var(--color-border-success);
  --textbox-border-error-color: var(--color-border-error);
  --textbox-border-warning-color: var(--color-border-warning);
  --textbox-border-info-color: var(--color-border-info);

  /* Input */
  --textbox-input-font: var(--font-body-1);
  --textbox-input-color: var(--color-text-primary);
  --textbox-input-padding: 0.65rem 0.5rem;
  --textbox-input-min-height: 2.1rem;
  --textbox-placeholder-color: var(--color-text-placeholder);
  --textbox-input-disabled-color: var(--color-text-disabled);
  --textbox-input-readonly-color: var(--color-text-secondary);

  /* Input Sizes */
  --textbox-input-padding-sm: 0.5rem 0.625rem;
  --textbox-input-min-height-sm: 1.7rem;
  --textbox-input-font-size-sm: 0.875rem;
  --textbox-input-padding-lg: 0.85rem 1rem;
  --textbox-input-min-height-lg: 2.5rem;
  --textbox-input-font-size-lg: 1.05rem;

  /* Input with Icons */
  --textbox-input-icon-padding-start: 2rem;
  --textbox-input-icon-padding-end: 2rem;

  /* Icons */
  --textbox-icon-color: var(--color-text-secondary);
  --textbox-icon-focus-color: var(--color-text-primary);
  --textbox-icon-disabled-color: var(--color-text-disabled);
  --textbox-icon-position-start: 0.75rem;
  --textbox-icon-position-end: 0.75rem;

  /* Prefix/Suffix */
  --textbox-affix-padding: 0 0.75rem;
  --textbox-affix-color: var(--color-text-secondary);
  --textbox-affix-font-size: 0.875rem;
  --textbox-affix-background: var(--color-background-subtle);
  --textbox-affix-border-radius: 0;
  --textbox-affix-border-color: var(--color-border-subtle);
  --textbox-affix-disabled-color: var(--color-text-disabled);
  --textbox-affix-disabled-background: var(--color-background-disabled);

  /* Error */
  --textbox-error-gap: var(--global-spacing-1);
  --textbox-error-color: var(--color-text-error);
  --textbox-error-font-size: 0.875rem;

  /* Helper */
  --textbox-helper-gap: var(--global-spacing-1);
  --textbox-helper-color: var(--color-text-secondary);
  --textbox-helper-font-size: 0.875rem;

  /* Textarea */
  --textbox-textarea-min-height: 5rem;

  /* Border Width */
  --textbox-border-width: 0.5px;

  /* =======================
     PLAY+ METAPHOR SEMANTIC TOKENS - Textbox Component
     These semantic tokens connect the base metaphor system to textbox-specific usage
     ======================= */

  /* Play+ Glass Metaphor - Semantic Textbox Tokens */
  --textbox-glass-background: var(--active-glass, var(--glass-25));
  --textbox-glass-background-hover: var(--active-glass, var(--glass-50));
  --textbox-glass-background-focus: var(--active-glass, var(--glass-75));
  --textbox-glass-border: 1px solid var(--color-border-default);
  --textbox-glass-border-hover: 1px solid rgba(var(--effect-color-surface), 0.4);
  --textbox-glass-border-focus: 1px solid rgba(var(--effect-color-primary), 0.6);
  --textbox-glass-backdrop-filter: var(--active-glass-blur, var(--glass-blur-25));
  --textbox-glass-backdrop-filter-hover: var(--active-glass-blur, var(--glass-blur-50));
  --textbox-glass-backdrop-filter-focus: var(--active-glass-blur, var(--glass-blur-75));

  /* Play+ Light Metaphor - Semantic Textbox Tokens */
  --textbox-light-shadow: var(--active-light, var(--ambient-25));
  --textbox-light-shadow-hover: var(--active-light, var(--glow-50));
  --textbox-light-shadow-focus: var(--active-light, var(--glow-75));
  --textbox-light-ring-focus: 0 0 0 3px rgba(var(--effect-color-primary), 0.3);
  --textbox-light-glow-error: var(--shimmer-100);
  --textbox-light-glow-success: var(--pulse-glow-75);

  /* Play+ Liquid Metaphor - Semantic Textbox Tokens */
  --textbox-liquid-transition: var(--active-duration, var(--liquid-duration-50)) var(--active-motion, var(--elastic-25));
  --textbox-liquid-transition-border: border-color var(--active-duration, var(--liquid-duration-50)) var(--active-motion, var(--elastic-25));
  --textbox-liquid-transition-shadow: box-shadow var(--active-duration, var(--liquid-duration-50)) var(--active-motion, var(--elastic-25));
  --textbox-liquid-transition-background: background var(--active-duration, var(--liquid-duration-50)) var(--active-motion, var(--elastic-25));
  --textbox-liquid-transform-hover: var(--ripple-10);
  --textbox-liquid-transform-focus: var(--ripple-25);
  --textbox-animation: var(--textbox-animation-none);
  --textbox-animation-hover: var(--textbox-animation-none);
  --textbox-animation-focus: var(--textbox-animation-none);

  /* Play+ Gradient Metaphor - Semantic Textbox Tokens */
  --textbox-gradient-background: var(--active-gradient, var(--gradient-0));
  --textbox-gradient-background-hover: var(--active-gradient, var(--gradient-25));
  --textbox-gradient-background-focus: var(--active-gradient, var(--gradient-50));
  --textbox-gradient-border: linear-gradient(135deg, rgba(var(--effect-color-primary), 0.2), rgba(var(--effect-color-secondary), 0.2));
  --textbox-gradient-border-focus: linear-gradient(135deg, rgba(var(--effect-color-primary), 0.6), rgba(var(--effect-color-secondary), 0.6));

  /* Legacy Semantic Tokens - For backward compatibility */
  --textbox-background: var(--textbox-glass-background);
  --textbox-background-hover: var(--textbox-glass-background-hover);
  --textbox-background-disabled: var(--glass-10);
  --textbox-focus-shadow: var(--textbox-light-shadow-focus);
  --textbox-hover-shadow: var(--textbox-light-shadow-hover);
  --textbox-error-shadow: var(--textbox-light-glow-error);
  --textbox-transition: var(--textbox-liquid-transition-border), var(--textbox-liquid-transition-shadow);
  --textbox-gradient: var(--textbox-gradient-background);

  /* =======================
     TEXTBOX INTENSITY SCALES - Play+ Metaphor System
     Maps global intensity scales to textbox-specific semantic tokens
     ======================= */

  /* Glass (Surface) Intensity - Textbox Specific */
  --textbox-surface-0:   var(--glass-0);
  --textbox-surface-10:  var(--glass-10);
  --textbox-surface-25:  var(--glass-25);
  --textbox-surface-50:  var(--glass-50);
  --textbox-surface-75:  var(--glass-75);
  --textbox-surface-100: var(--glass-100);

  /* Glass Blur - Textbox Specific */
  --textbox-surface-blur-0:   var(--glass-backdrop-0);
  --textbox-surface-blur-10:  var(--glass-backdrop-10);
  --textbox-surface-blur-25:  var(--glass-backdrop-25);
  --textbox-surface-blur-50:  var(--glass-backdrop-50);
  --textbox-surface-blur-75:  var(--glass-backdrop-75);
  --textbox-surface-blur-100: var(--glass-backdrop-100);

  /* Glass Border - Textbox Specific */
  --textbox-glass-border-0:   var(--glass-border-0);
  --textbox-glass-border-10:  var(--glass-border-10);
  --textbox-glass-border-25:  var(--glass-border-25);
  --textbox-glass-border-50:  var(--glass-border-50);
  --textbox-glass-border-75:  var(--glass-border-75);
  --textbox-glass-border-100: var(--glass-border-100);

  /* Glass Shadow - Textbox Specific */
  --textbox-glass-shadow-0:   var(--glass-shadow-0);
  --textbox-glass-shadow-10:  var(--glass-shadow-10);
  --textbox-glass-shadow-25:  var(--glass-shadow-25);
  --textbox-glass-shadow-50:  var(--glass-shadow-50);
  --textbox-glass-shadow-75:  var(--glass-shadow-75);
  --textbox-glass-shadow-100: var(--glass-shadow-100);

  /* Glass Text Color - Textbox Specific (theme-aware) */
  --textbox-glass-text-0:   var(--global-color-gray-800);  /* Dark text for solid glass */
  --textbox-glass-text-10:  var(--global-color-gray-800);  /* Dark text for light glass */
  --textbox-glass-text-25:  var(--global-color-gray-800);  /* Dark text for light glass */
  --textbox-glass-text-50:  var(--color-text-primary);     /* Use theme text */
  --textbox-glass-text-75:  var(--color-text-primary);     /* Use theme text */
  --textbox-glass-text-100: var(--color-text-primary);     /* Use theme text */

  /* Light (Feedback) Intensity - Textbox Specific */
  --textbox-light-0:   var(--light-0);
  --textbox-light-10:  var(--ambient-10);
  --textbox-light-25:  var(--ambient-25);
  --textbox-light-50:  var(--glow-50);
  --textbox-light-75:  var(--glow-75);
  --textbox-light-100: var(--neon-glow-100);

  /* Light Ring - Textbox Specific */
  --textbox-light-ring-0:   none;
  --textbox-light-ring-10:  0 0 0 2px rgba(var(--effect-color-primary), 0.1);
  --textbox-light-ring-25:  0 0 0 2px rgba(var(--effect-color-primary), 0.25);
  --textbox-light-ring-50:  0 0 0 3px rgba(var(--effect-color-primary), 0.3);
  --textbox-light-ring-75:  0 0 0 4px rgba(var(--effect-color-primary), 0.4);
  --textbox-light-ring-100: 0 0 0 4px rgba(var(--effect-color-primary), 0.5);

  /* Liquid (Motion) Intensity - Textbox Specific */
  --textbox-motion-0:   var(--elastic-0);
  --textbox-motion-10:  var(--elastic-10);
  --textbox-motion-25:  var(--elastic-25);
  --textbox-motion-50:  var(--ripple-50);
  --textbox-motion-75:  var(--ripple-75);
  --textbox-motion-100: var(--splash-100);

  /* Motion Duration - Textbox Specific */
  --textbox-motion-duration-0:   var(--liquid-duration-0);
  --textbox-motion-duration-10:  var(--liquid-duration-10);
  --textbox-motion-duration-25:  var(--liquid-duration-25);
  --textbox-motion-duration-50:  var(--liquid-duration-50);
  --textbox-motion-duration-75:  var(--liquid-duration-75);
  --textbox-motion-duration-100: var(--liquid-duration-100);

  /* Animation - Textbox Specific */
  --textbox-animation-none: none;
  --textbox-animation-pulse-10: var(--pulse-animation-10);
  --textbox-animation-pulse-25: var(--pulse-animation-25);
  --textbox-animation-pulse-50: var(--pulse-animation-50);
  --textbox-animation-pulse-75: var(--pulse-animation-75);
  --textbox-animation-pulse-100: var(--pulse-animation-100);

  /* Gradient Intensity - Textbox Specific */
  --textbox-gradient-0:   var(--gradient-0);
  --textbox-gradient-10:  var(--gradient-10);
  --textbox-gradient-25:  var(--gradient-25);
  --textbox-gradient-50:  var(--gradient-50);
  --textbox-gradient-75:  var(--gradient-75);
  --textbox-gradient-100: var(--gradient-100);

}

/* =======================
   TEXTBOX PERSONALITY OVERRIDES - Component-Level Token Overrides
   Override existing semantic tokens when specific personalities are active
   ======================= */

/* Minimal Personality - Override existing textbox semantic tokens */
[data-personality="minimal"] {
  /* Override glass metaphor tokens */
  --textbox-glass-background: var(--glass-0);
  --textbox-glass-background-hover: var(--glass-0);
  --textbox-glass-background-focus: var(--glass-0);
  
  
  /* Override light metaphor tokens */
  --textbox-light-shadow: none;
  --textbox-light-shadow-hover: var(--border-glow-10);
  --textbox-light-shadow-focus: var(--neon-glow-10);
  --textbox-light-ring-focus: 0 0 0 0 transparent;
  
  /* Override liquid metaphor tokens */
  --textbox-liquid-transform-hover: none;
  --textbox-liquid-transform-focus: none;
  --textbox-liquid-transition-border: none;
  --textbox-liquid-transition-shadow: none;
  --textbox-liquid-transition-background: none;
}

/* Professional Personality - Override existing textbox semantic tokens */
[data-personality="professional"] {
  /* Override glass metaphor tokens */
  --textbox-glass-background: var(--glass-10);
  --textbox-glass-background-hover: var(--glass-10);
  --textbox-glass-background-focus: var(--glass-10);
  
  /* Override light metaphor tokens */
  --textbox-light-shadow: none;
  --textbox-light-shadow-hover: var(--border-glow-25);
  --textbox-light-shadow-focus: var(--neon-glow-25);
  --textbox-light-ring-focus: 0 0 0 0 transparent;
  
  /* Override liquid metaphor tokens */
  --textbox-liquid-transform-hover: none;
  --textbox-liquid-transform-focus: none;
  --textbox-liquid-transition-border: none;
  --textbox-liquid-transition-shadow: none;
  --textbox-liquid-transition-background: none;
}

/* Modern Personality - Override existing textbox semantic tokens */
[data-personality="modern"] {
  /* Override glass metaphor tokens */
  --textbox-glass-background: var(--glass-0);
  --textbox-glass-background-hover: var(--glass-0);
  --textbox-glass-background-focus: var(--glass-0);
  
  /* Override light metaphor tokens */
  --textbox-light-shadow: none;
  --textbox-light-shadow-hover: var(--neon-glow-10);
  --textbox-light-shadow-focus: var(--box-shadow-glow-25);
  --textbox-light-ring-focus: 0 0 0 0 transparent;
  
  /* Override liquid metaphor tokens */
  --textbox-liquid-transform-hover: none;
  --textbox-liquid-transform-focus: none;
  --textbox-liquid-transition-border: none;
  --textbox-liquid-transition-shadow: none;
  --textbox-liquid-transition-background: none;
}

/* Vibrant Personality - Override existing textbox semantic tokens */
[data-personality="vibrant"] {
  /* Override glass metaphor tokens */
  --textbox-glass-background: var(--glass-0);
  --textbox-glass-background-hover: var(--glass-0);
  --textbox-glass-background-focus: var(--glass-0);
  
  /* Override light metaphor tokens */
  --textbox-light-shadow: none;
  --textbox-light-shadow-hover: var(--pulse-glow-25); 
  --textbox-light-shadow-focus: var(--box-shadow-glow-50);
  --textbox-light-ring-focus: 0 0 0 0 transparent;
  
  /* Override animation tokens - use none since we apply animate.css directly */
  --textbox-animation: var(--textbox-animation-none);
  --textbox-animation-hover: var(--textbox-animation-none);
  --textbox-animation-focus: var(--textbox-animation-none);
  
  /* Override liquid metaphor tokens */
  --textbox-liquid-transform-hover: none;
  --textbox-liquid-transform-focus: none;
  --textbox-liquid-transition-border: none;
  --textbox-liquid-transition-shadow: none;
  --textbox-liquid-transition-background: none;
}