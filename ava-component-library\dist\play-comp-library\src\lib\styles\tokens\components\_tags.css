/**
 * Component: Tags
 * Purpose: Tags component tokens for categorization and labeling
 */

:root {
  /* Tags Base */
  --tags-background: var(--color-background-primary);
  --tags-border-radius: var(--global-radius-sm);
  --tags-padding: var(--global-spacing-2) var(--global-spacing-3);
  --tags-font: var(--font-body-2);
  --tags-transition: all var(--global-motion-duration-standard) var(--global-motion-easing-standard);

  /* Tags Text */
  --tags-text-color: var(--color-text-primary);
  --tags-text-font: var(--font-body-2);
  --tags-text-size: var(--global-font-size-sm);
  --tags-text-size-small: calc(var(--global-font-size-sm) - (var(--global-font-size-sm) * 0.125));

  /* Tags Variants: Filled */
  --tags-filled-background: var(--color-surface-subtle);
  --tags-filled-text: var(--color-text-primary);
  --tags-filled-border: 1.5px solid var(--color-border-default);

  --tags-filled-primary-background: var(--color-surface-interactive-default);
  --tags-filled-primary-text: var(--color-text-on-brand);
  --tags-filled-primary-border: 1.5px solid var(--color-border-interactive);

  --tags-filled-success-background: var(--color-background-success);
  --tags-filled-success-text: var(--color-text-on-brand);
  --tags-filled-success-border: 1.5px solid var(--color-border-success);

  --tags-filled-warning-background: var(--color-background-warning);
  --tags-filled-warning-text: var(--color-text-primary);
  --tags-filled-warning-border: 1.5px solid var(--color-border-warning);

  --tags-filled-error-background: var(--color-background-error);
  --tags-filled-error-text: var(--color-text-on-brand);
  --tags-filled-error-border: 1.5px solid var(--color-border-error);

  --tags-filled-info-background: var(--color-background-info);
  --tags-filled-info-text: var(--color-text-on-brand);
  --tags-filled-info-border: 1.5px solid var(--color-border-info);

  --tags-filled-custom-background: var(--color-background-custom);
  --tags-filled-custom-text: var(--color-text-custom);
  --tags-filled-custom-border: 1.5px solid var(--color-border-custom);

  /* Tags Variants: Outlined */
  --tags-outlined-background: transparent;
  --tags-outlined-text: var(--color-text-secondary);
  --tags-outlined-border: 1.5px solid var(--color-border-default);

  --tags-outlined-primary-text: var(--color-surface-interactive-default);
  --tags-outlined-primary-border: 1.5px solid var(--color-border-interactive);

  --tags-outlined-success-text: var(--color-text-success);
  --tags-outlined-success-border: 1.5px solid var(--color-border-success);

  --tags-outlined-warning-text: var(--color-text-warning);
  --tags-outlined-warning-border: 1.5px solid var(--color-border-warning);

  --tags-outlined-error-text: var(--color-text-error);
  --tags-outlined-error-border: 1.5px solid var(--color-border-error);

  --tags-outlined-info-text: var(--color-text-info);
  --tags-outlined-info-border: 1.5px solid var(--color-border-info);

  --tags-outlined-custom-text: var(--color-text-custom);
  --tags-outlined-custom-border: 1.5px solid var(--color-border-custom);

  /* Tags States */
  --tags-hover-background: var(--color-surface-subtle-hover);
  --tags-hover-text: var(--color-text-primary);
  --tags-hover-border: 1.5px solid var(--color-border-default);

  --tags-focus-background: var(--color-background-primary);
  --tags-focus-text: var(--color-text-primary);
  --tags-focus-border: 2px solid var(--color-border-focus);
  --tags-focus-outline: none;

  --tags-disabled-background: var(--color-background-disabled);
  --tags-disabled-text: var(--color-text-disabled);
  --tags-disabled-border: 1.5px solid var(--color-border-disabled);
  --tags-disabled-cursor: not-allowed;

  /* Tags Icon */
  --tags-icon-size: var(--global-icon-size-sm);
  --tags-icon-color: var(--color-text-secondary);
  --tags-icon-margin: var(--global-spacing-1);

  --tags-icon-filled-color: var(--color-text-primary);
  --tags-icon-outlined-color: var(--color-text-secondary);

  /* Tags Removable */
  --tags-removable-button-background: transparent;
  --tags-removable-button-text: var(--color-text-secondary);
  --tags-removable-button-size: var(--global-icon-size-xs);
  --tags-removable-button-border-radius: var(--global-radius-circle);
  --tags-removable-button-padding: var(--global-spacing-1);

  --tags-removable-button-hover-background: var(--color-surface-subtle-hover);
  --tags-removable-button-hover-text: var(--color-text-primary);

  --tags-removable-button-active-background: var(--color-surface-subtle);
  --tags-removable-button-active-text: var(--color-text-primary);

  /* Tags Group */
  --tags-group-gap: var(--global-spacing-2);
  --tags-group-padding: var(--global-spacing-3);
  --tags-group-border: 1.5px solid var(--color-border-subtle);
  --tags-group-border-radius: var(--global-radius-md);
  --tags-group-background: var(--color-background-secondary);

  /* Tags Input */
  --tags-input-background: transparent;
  --tags-input-text: var(--color-text-primary);
  --tags-input-border: none;
  --tags-input-outline: none;
  --tags-input-padding: var(--global-spacing-1);
  --tags-input-font: var(--font-body-2);

  --tags-input-placeholder: var(--color-text-placeholder);
  --tags-input-focus-border: none;
  --tags-input-focus-outline: none;

  /* Tag Pill Variant */
  --tags-pill-border-radius: 999px;

  /* Tag Avatar */
  --tags-avatar-bg: var(--color-avatar-bg);
  --tags-avatar-initials-bg: var(--color-avatar-initials-bg);
  --tags-avatar-initials-color: var(--color-avatar-initials-color);
  --tags-avatar-size: var(--global-avatar-size, 1.5em);
  --tags-avatar-font-size: var(--global-avatar-font-size, 0.85em);
  --tags-avatar-margin: var(--global-avatar-margin, 0.4em);

  /* Tag Label */
  --tags-label-font-weight: var(--global-label-font-weight, 500);
  --tags-label-letter-spacing: var(--global-label-letter-spacing, 0.01em);
  --tags-label-max-width: var(--global-label-max-width, 12em);

  /* Tag Custom Color Support */
  --tag-custom-bg: var(--tags-filled-custom-background);
  --tag-custom-color: var(--tags-filled-custom-text);
  --tag-custom-border: var(--tags-filled-custom-border);

  /* Tag Sizes */
  --tags-size-sm-font: var(--global-font-size-xs);
  --tags-size-sm-padding: var(--global-spacing-1) var(--global-spacing-2);
  --tags-size-sm-border-radius: var(--global-radius-sm);

  --tags-size-md-font: var(--global-font-size-sm);
  --tags-size-md-padding: var(--global-spacing-2) var(--global-spacing-3);
  --tags-size-md-border-radius: var(--global-radius-sm);

  --tags-size-lg-font: var(--global-font-size-md);
  --tags-size-lg-padding: var(--global-spacing-3) var(--global-spacing-4);
  --tags-size-lg-border-radius: var(--global-radius-md);
} 