import * as i0 from "@angular/core";
export type SnackbarPosition = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center' | 'center';
export interface SnackbarData {
    message: string;
    duration: number;
    position: SnackbarPosition;
    color: string;
    backgroundColor: string;
}
export declare class SnackbarService {
    private snackbarSignal;
    readonly snackbar$: import("@angular/core").Signal<SnackbarData | null>;
    show(message: string, position?: SnackbarPosition, duration?: number, color?: string, backgroundColor?: string): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<SnackbarService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<SnackbarService>;
}
