/**
 * Component: Avatar
 * Purpose: Avatar component tokens for user profile images and status indicators
 */

:root {
  /* Avatar Base */
  --avatar-size-sm: 2rem;
  --avatar-size-md: 3rem;
  --avatar-size-lg: 4rem;
  --avatar-size-xl: 5rem;

  /* Avatar Border */
  --avatar-border-radius: var(--global-radius-circle);
  --avatar-border-width: 0.125rem;
  --avatar-border-color: var(--color-border-subtle);

  /* Avatar Background */
  --avatar-background: var(--color-surface-subtle);
  --avatar-fallback-background: var(--color-surface-subtle);

  /* Avatar Text */
  --avatar-text-color: var(--color-text-secondary);
  --avatar-text-font: var(--font-label);
  --avatar-text-size-sm: var(--global-font-size-xs);
  --avatar-text-size-md: var(--global-font-size-sm);
  --avatar-text-size-lg: var(--global-font-size-md);
  --avatar-text-size-xl: var(--global-font-size-lg);

  /* Avatar Status Badge */
  --avatar-status-size: 0.75rem;
  --avatar-status-border-width: 0.125rem;
  --avatar-status-border-color: var(--color-background-primary);
  --avatar-status-online: var(--global-color-green-500);
  --avatar-status-offline: var(--global-color-gray-400);
  --avatar-status-away: var(--global-color-yellow-500);
  --avatar-status-busy: var(--global-color-red-500);

  /* Avatar Notification Badge */
  --avatar-notification-size: 1.25rem;
  --avatar-notification-background: var(--color-surface-error);
  --avatar-notification-text: var(--color-text-on-brand);
  --avatar-notification-font: var(--font-label);
  --avatar-notification-border-radius: var(--global-radius-pill);
  --avatar-notification-border: 0.125rem solid var(--color-background-primary);

  /* Avatar Group */
  --avatar-group-spacing: -0.5rem;
  --avatar-group-border-width: 0.125rem;
  --avatar-group-border-color: var(--color-background-primary);
} 