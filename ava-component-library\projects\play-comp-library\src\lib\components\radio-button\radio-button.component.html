<div class="radio-group" [attr.data-orientation]="orientation">
    <label class="radio-wrapper" *ngFor="let option of options" [attr.data-size]="size" [ngClass]="{
            'disabled': option.disabled
        }">
        <input type="radio" class="radio-input" [name]="name" [value]="option.value"
            [checked]="selectedValue === option.value" (change)="onSelectionChange(option.value)"
            [disabled]="option.disabled" />
        <span class="custom-radio" [ngStyle]="{
        border: option.disabled
          ? 'var(--radio-checkmark-border-disabled)'
          : (color
              ? '2px solid ' + color
              : 'var(--radio-checkmark-border)'),
        backgroundColor: option.disabled
          ? 'var(--radio-checkmark-background-disabled)'
          : 'var(--radio-checkmark-background)'
      }" [class.animated-shadow]="animation === 'shadow' && selectedValue === option.value"
            [class.disabled]="option.disabled">
            <span class="radio-dot" *ngIf="selectedValue === option.value && radio !== 'none'" [ngStyle]="{
          backgroundColor: option.disabled
            ? 'var(--radio-dot-background-disabled)'
            : (color || 'var(--radio-dot-background)')
        }"></span>
        </span>
        <span class="radio-label" [ngStyle]="labelColor ? { color: labelColor } : { color: 'var(--radio-label-color)' }"
            [class.disabled]="option.disabled">
            {{ option.label }}
        </span>
    </label>
</div>