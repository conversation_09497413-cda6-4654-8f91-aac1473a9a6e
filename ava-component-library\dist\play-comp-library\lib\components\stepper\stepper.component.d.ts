import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class AvaStepperComponent {
    steps: string[];
    currentStep: number;
    orientation: 'horizontal' | 'vertical';
    showNavigation: boolean;
    interactive: boolean;
    size: 'small' | 'medium' | 'large';
    disabledSteps: number[];
    iconColor: string;
    iconSize: string;
    stepChange: EventEmitter<number>;
    submit: EventEmitter<void>;
    isDisabled(i: number): boolean;
    goToStep(index: number): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<AvaStepperComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<AvaStepperComponent, "ava-stepper", never, { "steps": { "alias": "steps"; "required": false; }; "currentStep": { "alias": "currentStep"; "required": false; }; "orientation": { "alias": "orientation"; "required": false; }; "showNavigation": { "alias": "showNavigation"; "required": false; }; "interactive": { "alias": "interactive"; "required": false; }; "size": { "alias": "size"; "required": false; }; "disabledSteps": { "alias": "disabledSteps"; "required": false; }; "iconColor": { "alias": "iconColor"; "required": false; }; "iconSize": { "alias": "iconSize"; "required": false; }; }, { "stepChange": "stepChange"; "submit": "submit"; }, never, never, true, never>;
}
