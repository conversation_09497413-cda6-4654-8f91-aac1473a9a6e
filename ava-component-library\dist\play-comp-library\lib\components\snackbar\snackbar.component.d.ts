import { SnackbarService } from './snackbar.service';
import * as i0 from "@angular/core";
export declare class SnackbarComponent {
    snackbarService: SnackbarService;
    snackbar$: import("@angular/core").Signal<import("@ava/play-comp-library").SnackbarData | null>;
    static ɵfac: i0.ɵɵFactoryDeclaration<SnackbarComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SnackbarComponent, "ava-snackbar", never, {}, {}, never, never, true, never>;
}
