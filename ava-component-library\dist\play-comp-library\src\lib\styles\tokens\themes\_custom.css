/**
 * Theme: Custom
 * Purpose: Custom theme with #626780 as primary color
 */

[data-theme="custom"] {
  /* Custom Color Palette */
  --global-color-primary: #626780;
  --global-color-primary-50: #f5f5f7;
  --global-color-primary-100: #e8e9ed;
  --global-color-primary-200: #d1d3dc;
  --global-color-primary-300: #b3b7c7;
  --global-color-primary-400: #8f94a8;
  --global-color-primary-500: #626780;
  --global-color-primary-600: #5a5f73;
  --global-color-primary-700: #4d5162;
  --global-color-primary-800: #414552;
  --global-color-primary-900: #373a44;
  --global-color-primary-950: #2a2c33;

  /* Semantic Color Overrides */
  --color-brand-primary: var(--global-color-primary-500);
  --color-brand-primary-hover: var(--global-color-primary-600);
  --color-brand-primary-active: var(--global-color-primary-700);
  --color-brand-primary-disabled: var(--global-color-primary-200);

  /* Interactive Surface Colors */
  --color-surface-interactive-default: var(--global-color-primary-500);
  --color-surface-interactive-hover: var(--global-color-primary-600);
  --color-surface-interactive-active: var(--global-color-primary-700);
  --color-surface-interactive-disabled: var(--global-color-primary-200);

  /* Border Colors */
  --color-border-interactive: var(--global-color-primary-500);
  --color-border-focus: var(--global-color-primary-500);

  /* Text Colors */
  --color-text-interactive: var(--global-color-primary-600);
  --color-text-interactive-hover: var(--global-color-primary-700);

  /* Component-Specific Overrides */
  --button-primary-background: var(--global-color-primary-500);
  --button-primary-text: var(--global-color-white);
  --button-primary-hover-background: var(--global-color-primary-600);
  --button-primary-active-background: var(--global-color-primary-700);
  --button-primary-disabled-background: var(--global-color-primary-200);

  --input-focus-border-color: var(--global-color-primary-500);
  --input-focus-shadow: 0 0 0 2px var(--global-color-primary-100);

  --badge-primary-background: var(--global-color-primary-100);
  --badge-primary-text: var(--global-color-primary-700);

  --toast-primary-background: var(--global-color-primary-500);
  --toast-primary-text: var(--global-color-white);

  --dropdown-item-selected-background: var(--global-color-primary-100);
  --dropdown-item-selected-text: var(--global-color-primary-700);

  --tooltip-background: var(--global-color-primary-800);
  --tooltip-text: var(--global-color-white);

  --progress-fill-background: var(--global-color-primary-500);

  --slider-thumb-background: var(--global-color-primary-500);
  --slider-track-fill-background: var(--global-color-primary-500);

  --radio-checked-background: var(--global-color-primary-500);
  --radio-checked-border-color: var(--global-color-primary-500);

  --checkbox-checked-background: var(--global-color-primary-500);
  --checkbox-checked-border-color: var(--global-color-primary-500);

  --spinner-color: var(--global-color-primary-500);

  --avatar-status-online: var(--global-color-primary-500);

  --accordion-header-active-background: var(--global-color-primary-100);

  --calendar-date-selected-background: var(--global-color-primary-500);
  --calendar-date-selected-text: var(--global-color-white);

  --link-text: var(--global-color-primary-600);
  --link-hover-text: var(--global-color-primary-700);

  --pagination-item-active-background: var(--global-color-primary-500);
  --pagination-item-active-text: var(--global-color-white);

  --search-focus-border: 1px solid var(--global-color-primary-500);
  --search-focus-shadow: 0 0 0 2px var(--global-color-primary-100);

  --toggle-track-checked-background: var(--global-color-primary-500);
  --toggle-track-checked-border: 1px solid var(--global-color-primary-500);

  --breadcrumbs-item-hover-background: var(--global-color-primary-100);
  --breadcrumbs-dropdown-item-active-background: var(--global-color-primary-500);
  --breadcrumbs-dropdown-item-active-text: var(--global-color-white);

  /* Accessibility */
  --accessibility-focus-ring-color: var(--global-color-primary-500);
} 