import * as i0 from "@angular/core";
export declare class LinkComponent {
    label: string;
    color: 'success' | 'warning' | 'danger' | 'info' | 'default' | 'primary' | string;
    size: 'small' | 'medium' | 'large';
    underline: boolean;
    isHexColor(color: string): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<LinkComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<LinkComponent, "ava-link", never, { "label": { "alias": "label"; "required": false; }; "color": { "alias": "color"; "required": false; }; "size": { "alias": "size"; "required": false; }; "underline": { "alias": "underline"; "required": false; }; }, {}, never, never, true, never>;
}
