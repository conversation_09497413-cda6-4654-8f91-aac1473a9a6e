import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export type ToggleSize = 'small' | 'medium' | 'large';
export type TogglePosition = 'left' | 'right';
export declare class ToggleComponent {
    size: ToggleSize;
    title: string;
    position: TogglePosition;
    disabled: boolean;
    checked: boolean;
    animation: boolean;
    checkedChange: EventEmitter<boolean>;
    onToggle(): void;
    onKeyDown(event: KeyboardEvent): void;
    get titleName(): string | null;
    static ɵfac: i0.ɵɵFactoryDeclaration<ToggleComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ToggleComponent, "ava-toggle", never, { "size": { "alias": "size"; "required": false; }; "title": { "alias": "title"; "required": false; }; "position": { "alias": "position"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "checked": { "alias": "checked"; "required": false; }; "animation": { "alias": "animation"; "required": false; }; }, { "checkedChange": "checkedChange"; }, never, never, true, never>;
}
