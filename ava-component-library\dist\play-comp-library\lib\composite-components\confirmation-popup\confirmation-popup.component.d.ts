import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class ConfirmationPopupComponent {
    confirmationLabel: string;
    title: string;
    message: string;
    show: boolean;
    closed: EventEmitter<void>;
    handleConfirm(text: string): void;
    handleCancel(): void;
    handleClose(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<ConfirmationPopupComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ConfirmationPopupComponent, "ava-confirmation-popup", never, { "confirmationLabel": { "alias": "confirmationLabel"; "required": false; }; "title": { "alias": "title"; "required": false; }; "message": { "alias": "message"; "required": false; }; "show": { "alias": "show"; "required": false; }; }, { "closed": "closed"; }, never, never, true, never>;
}
