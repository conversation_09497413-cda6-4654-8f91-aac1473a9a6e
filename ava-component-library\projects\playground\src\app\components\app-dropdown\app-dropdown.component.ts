import { Compo<PERSON>, ElementRef, HostListener, ViewChild, ViewEncapsulation } from '@angular/core';
import { ButtonComponent, DropdownComponent, DropdownOption } from '../../../../../play-comp-library/src/public-api';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-app-dropdown',
  imports: [DropdownComponent, CommonModule, ReactiveFormsModule, ButtonComponent],
  templateUrl: './app-dropdown.component.html',
  styleUrls: ['./app-dropdown.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppDropdownComponent {
  testForm: FormGroup;
  sections = [
    {
      title: 'Basic Usage',
      description: 'Demonstrates the basic functionality of the dropdown with and without sub-options.',
      showCode: false,
    },
    {
      title: 'With Default Selection',
      description: 'Shows how to set a default selected option when the dropdown loads.',
      showCode: false,
    },
    {
      title: 'With Search',
      description: 'Demonstrates dropdown functionality with search enabled.',
      showCode: false,
    },
    {
      title: 'Options with Icons',
      description: 'Shows how to add icons to the dropdown options.',
      showCode: false,
    },
    {
      title: 'Custom Dropdown Icons',
      description: 'Demonstrates different icons for the dropdown toggle button.',
      showCode: false,
    },
    {
      title: 'Custom Option Icons',
      description: 'Shows how to add custom icons to individual dropdown options.',
      showCode: false,
    },
    {
      title: 'Multi-select',
      description: 'Illustrates the multi-select functionality.',
      showCode: false,
    },
    {
      title: 'Single-select',
      description: 'Illustrates the single-select functionality.',
      showCode: false,
    },
    {
      title: 'Disabled Dropdown',
      description: 'Shows how to disable the entire dropdown component.',
      showCode: false,
    },
    {
      title: 'Disabled Options',
      description: 'Demonstrates how to disable individual options and sub-options.',
      showCode: false,
    },
    {
      title: 'Label and Required Fields',
      description: 'Shows how to add labels and required field validation to dropdowns.',
      showCode: false,
    }
  ];


  apiProps = [
    { name: 'dropdownTitle', type: 'string', default: 'Select a Category', description: 'The title displayed when no option is selected.' },
    { name: 'options', type: 'DropdownOption[]', default: '[]', description: 'The list of options to display in the dropdown.' },
    { name: 'suboptions', type: '{ [key: string]: DropdownOption[] }', default: '{}', description: 'A map of options to their respective sub-options.' },
    { name: 'selectedValue', type: 'string', default: "''", description: 'The name of the option to be selected by default.' },
    { name: 'dropdownIcon', type: 'string', default: 'chevron-down', description: 'The icon to display in the dropdown toggle button.' },
    { name: 'checkboxOptions', type: 'string[]', default: '[]', description: 'The list of options that will display a checkbox.' },
    { name: 'search', type: 'boolean', default: 'false', description: 'Whether to display the search functionality.' },
    { name: 'singleSelect', type: 'boolean', default: 'false', description: 'Whether checkboxes allow single selection only.' },
    { name: 'disabled', type: 'boolean', default: 'false', description: 'Disables the entire dropdown component.' },
    { name: 'label', type: 'string', default: "''", description: 'Label text to display above the dropdown.' },
    { name: 'required', type: 'boolean', default: 'false', description: 'Whether the field is required. Shows validation error if opened/closed without selection.' }
  ];

  interfaceProps = [
    { name: 'DropdownOption.name', type: 'string', description: 'The display name of the option.' },
    { name: 'DropdownOption.value', type: 'string | number', description: 'The value of the option.' },
    { name: 'DropdownOption.icon', type: 'string (optional)', description: 'The Lucide icon name to display for this option.' },
    { name: 'DropdownOption.disabled', type: 'boolean (optional)', description: 'Whether this specific option is disabled.' }
  ];

  events = [
    { name: 'selectionChange', type: 'EventEmitter<any>', description: 'Emitted when the selection changes.' }
  ];

  // Sample data for dropdown examples
  basicOptions: DropdownOption[] = [
    { name: 'Electronics', value: 'electronics' },
    { name: 'Clothing', value: 'clothing' },
    { name: 'Books', value: 'books' }
  ];

  basicSuboptions = {
    'Electronics': [
      { name: 'Phones', value: 'phones' },
      { name: 'Laptops', value: 'laptops' }
    ],
    'Clothing': [
      { name: 'Shirts', value: 'shirts' },
      { name: 'Pants', value: 'pants' }
    ],
    'Books': [
      { name: 'Fiction', value: 'fiction' },
      { name: 'Non-Fiction', value: 'non-fiction' }
    ]
  };

  simpleOptions: DropdownOption[] = [
    { name: 'Option 1', value: '1' },
    { name: 'Option 2', value: '2' },
    { name: 'Option 3', value: '3' },
    { name: 'Option 4', value: '4' },
    { name: 'Option 5', value: '5' },
    { name: 'Option 6', value: '6' },
    { name: 'Option 7', value: '7' },
    { name: 'Option 8', value: '8' },
    { name: 'Option 9', value: '9' },
    { name: 'Option 10', value: '10' },
    { name: 'Option 11', value: '11' },
    { name: 'Option 12', value: '12' }
  ];

  checkboxOptions: DropdownOption[] = [
    { name: 'Feature A', value: 'A' },
    { name: 'Feature B', value: 'B' },
    { name: 'Feature C', value: 'C' }
  ];

  iconOptions: DropdownOption[] = [
    { name: 'Network', value: 'network', icon: 'wifi' },
    { name: 'Settings', value: 'settings', icon: 'settings' },
    { name: 'Profile', value: 'profile', icon: 'user' }
  ];

  customIconOptions: DropdownOption[] = [
    { name: 'Home', value: 'home', icon: 'home' },
    { name: 'Search', value: 'search', icon: 'search' },
    { name: 'Mail', value: 'mail', icon: 'mail' },
    { name: 'Phone', value: 'phone', icon: 'phone' },
    { name: 'Lock', value: 'lock', icon: 'lock' }
  ];

  // Options with some disabled items
  disabledOptions: DropdownOption[] = [
    { name: 'Available Option', value: 'available' },
    { name: 'Disabled Option', value: 'disabled', disabled: true },
    { name: 'Another Available', value: 'available2' },
    { name: 'Also Disabled', value: 'disabled2', disabled: true }
  ];

  // Options for default selection examples
  defaultSelectionOptions: DropdownOption[] = [
    { name: 'Apple', value: 'apple' },
    { name: 'Banana', value: 'banana' },
    { name: 'Orange', value: 'orange' },
    { name: 'Grape', value: 'grape' }
  ];

  defaultSelectionSuboptions = {
    'Apple': [
      { name: 'Red Apple', value: 'red-apple' },
      { name: 'Green Apple', value: 'green-apple' }
    ],
    'Banana': [
      { name: 'Yellow Banana', value: 'yellow-banana' },
      { name: 'Green Banana', value: 'green-banana' }
    ],
    'Orange': [
      { name: 'Sweet Orange', value: 'sweet-orange' },
      { name: 'Blood Orange', value: 'blood-orange' }
    ],
    'Grape': [
      { name: 'Red Grape', value: 'red-grape' },
      { name: 'Green Grape', value: 'green-grape' }
    ]
  };

  // Sub-options with some disabled items
  disabledSuboptions: { [key: string]: DropdownOption[] } = {
    'Categories': [
      { name: 'Electronics', value: 'electronics' },
      { name: 'Clothing (Disabled)', value: 'clothing', disabled: true },
      { name: 'Books', value: 'books' }
    ],
    'Services': [
      { name: 'Support', value: 'support' },
      { name: 'Premium (Disabled)', value: 'premium', disabled: true },
      { name: 'Basic', value: 'basic' }
    ]
  };

  // Checkbox options with some disabled
  disabledCheckboxOptions: DropdownOption[] = [
    { name: 'Feature A', value: 'A' },
    { name: 'Feature B (Disabled)', value: 'B', disabled: true },
    { name: 'Feature C', value: 'C' },
    { name: 'Feature D (Disabled)', value: 'D', disabled: true }
  ];

  selectedData: any = null;
  @ViewChild('codeBlock') codeBlock!: ElementRef;
  constructor(private fb: FormBuilder) {
    this.testForm = this.fb.group({
      category: [''],
    });
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    console.log('Selection changed:', data);
  }

  toggleSection(index: number): void {
    this.sections.forEach((section, i) => {
      section.showCode = (i === index) ? !section.showCode : false;
    });
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (!this.codeBlock?.nativeElement) return;
    const clickedElement = event.target as HTMLElement;
    const isClickOnSectionHeader = clickedElement.closest('.section-header');
    const clickedInside = this.codeBlock.nativeElement.contains(clickedElement) || isClickOnSectionHeader;
    if (!clickedInside) {
      this.sections.forEach(section => section.showCode = false);
    }
  }

  getDropdownCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'basic usage': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-dropdown',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <ava-dropdown
      dropdownTitle="Select Category"
      [options]="basicOptions"
      [suboptions]="basicSuboptions"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class BasicDropdownComponent {
  basicOptions: DropdownOption[] = [
    { name: 'Electronics', value: 'electronics' },
    { name: 'Clothing', value: 'clothing' },
    { name: 'Books', value: 'books' }
  ];

  basicSuboptions = {
    'Electronics': [
      { name: 'Phones', value: 'phones' },
      { name: 'Laptops', value: 'laptops' }
    ],
    'Clothing': [
      { name: 'Shirts', value: 'shirts' },
      { name: 'Pants', value: 'pants' }
    ]
  };

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`,
      'with default selection': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-default-selection-dropdown',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <ava-dropdown
      dropdownTitle="Select Fruit"
      [options]="fruitOptions"
      [suboptions]="fruitSuboptions"
      [selectedValue]="'Banana'"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class DefaultSelectionDropdownComponent {
  fruitOptions: DropdownOption[] = [
    { name: 'Apple', value: 'apple' },
    { name: 'Banana', value: 'banana' },
    { name: 'Orange', value: 'orange' },
    { name: 'Grape', value: 'grape' }
  ];

  fruitSuboptions = {
    'Apple': [
      { name: 'Red Apple', value: 'red-apple' },
      { name: 'Green Apple', value: 'green-apple' }
    ],
    'Banana': [
      { name: 'Yellow Banana', value: 'yellow-banana' },
      { name: 'Green Banana', value: 'green-banana' }
    ]
  };

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`,
      'with search': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-search-dropdown',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <ava-dropdown
      dropdownTitle="Search Categories"
      [options]="options"
      [search]="true"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class SearchDropdownComponent {
  options: DropdownOption[] = [
    { name: 'Electronics', value: 'electronics' },
    { name: 'Clothing', value: 'clothing' },
    { name: 'Books', value: 'books' }
  ];

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`,
      'options with icons': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-icon-dropdown',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <ava-dropdown
      dropdownTitle="Network Options"
      [options]="iconOptions"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class IconDropdownComponent {
  iconOptions: DropdownOption[] = [
    { name: 'Network', value: 'network', icon: 'wifi' },
    { name: 'Settings', value: 'settings', icon: 'settings' },
    { name: 'Profile', value: 'profile', icon: 'user' }
  ];

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`,
      'custom dropdown icons': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-custom-dropdown-icon',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <ava-dropdown
      dropdownTitle="Custom Toggle Icon"
      [dropdownIcon]="'circle-check'"
      [options]="options"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class CustomDropdownIconComponent {
  options: DropdownOption[] = [
    { name: 'Option A', value: 'A' },
    { name: 'Option B', value: 'B' },
    { name: 'Option C', value: 'C' }
  ];

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`,
      'custom option icons': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-custom-option-icons',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <ava-dropdown
      dropdownTitle="Navigation Options"
      [options]="customIconOptions"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class CustomOptionIconsComponent {
  customIconOptions: DropdownOption[] = [
    { name: 'Home', value: 'home', icon: 'home' },
    { name: 'Search', value: 'search', icon: 'search' },
    { name: 'Mail', value: 'mail', icon: 'mail' },
    { name: 'Phone', value: 'phone', icon: 'phone' },
    { name: 'Lock', value: 'lock', icon: 'lock' }
  ];

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`,
      'multi-select': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-multi-select-dropdown',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <ava-dropdown
      dropdownTitle="Multi-select Features"
      [options]="checkboxOptions"
      [checkboxOptions]="['Feature A', 'Feature B', 'Feature C']"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class MultiSelectDropdownComponent {
  checkboxOptions: DropdownOption[] = [
    { name: 'Feature A', value: 'A' },
    { name: 'Feature B', value: 'B' },
    { name: 'Feature C', value: 'C' }
  ];

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`,
      'single-select': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-single-select-dropdown',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <ava-dropdown
      dropdownTitle="Single Select"
      [options]="checkboxOptions"
      [checkboxOptions]="['Feature A', 'Feature B', 'Feature C']"
      [singleSelect]="true"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class SingleSelectDropdownComponent {
  checkboxOptions: DropdownOption[] = [
    { name: 'Feature A', value: 'A' },
    { name: 'Feature B', value: 'B' },
    { name: 'Feature C', value: 'C' }
  ];

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`,
      'disabled dropdown': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-disabled-dropdown',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <!-- Disabled Dropdown -->
    <ava-dropdown
      dropdownTitle="Disabled Dropdown"
      [options]="options"
      [disabled]="true"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>

    <!-- Normal Dropdown for comparison -->
    <ava-dropdown
      dropdownTitle="Normal Dropdown"
      [options]="options"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class DisabledDropdownComponent {
  options: DropdownOption[] = [
    { name: 'Option 1', value: '1' },
    { name: 'Option 2', value: '2' },
    { name: 'Option 3', value: '3' }
  ];

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`,
      'disabled options': `import { Component } from '@angular/core';
import { DropdownComponent, DropdownOption } from '@awe/play-comp-library';

@Component({
  selector: 'app-disabled-options',
  standalone: true,
  imports: [DropdownComponent],
  template: \`
    <!-- Mixed Options with some disabled -->
    <ava-dropdown
      dropdownTitle="Mixed Options"
      [options]="disabledOptions"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>

    <!-- Sub-options with disabled items -->
    <ava-dropdown
      dropdownTitle="Categories with Disabled Sub-options"
      [options]="mainOptions"
      [suboptions]="disabledSuboptions"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>

    <!-- Checkboxes with disabled items -->
    <ava-dropdown
      dropdownTitle="Disabled Checkboxes"
      [options]="disabledCheckboxOptions"
      [checkboxOptions]="['Feature A', 'Feature B (Disabled)', 'Feature C', 'Feature D (Disabled)']"
      (selectionChange)="onSelectionChange($event)">
    </ava-dropdown>
  \`
})
export class DisabledOptionsComponent {
  // Options with some disabled items
  disabledOptions: DropdownOption[] = [
    { name: 'Available Option', value: 'available' },
    { name: 'Disabled Option', value: 'disabled', disabled: true },
    { name: 'Another Available', value: 'available2' },
    { name: 'Also Disabled', value: 'disabled2', disabled: true }
  ];

  // Main options for sub-options demo
  mainOptions: DropdownOption[] = [
    { name: 'Categories', value: 'categories' },
    { name: 'Services', value: 'services' }
  ];

  // Sub-options with some disabled items
  disabledSuboptions: { [key: string]: DropdownOption[] } = {
    'Categories': [
      { name: 'Electronics', value: 'electronics' },
      { name: 'Clothing (Disabled)', value: 'clothing', disabled: true },
      { name: 'Books', value: 'books' }
    ],
    'Services': [
      { name: 'Support', value: 'support' },
      { name: 'Premium (Disabled)', value: 'premium', disabled: true },
      { name: 'Basic', value: 'basic' }
    ]
  };

  // Checkbox options with some disabled
  disabledCheckboxOptions: DropdownOption[] = [
    { name: 'Feature A', value: 'A' },
    { name: 'Feature B (Disabled)', value: 'B', disabled: true },
    { name: 'Feature C', value: 'C' },
    { name: 'Feature D (Disabled)', value: 'D', disabled: true }
  ];

  onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }
}`
    };

    return examples[sectionTitle.toLowerCase()] || '';
  }


  copyCode(sectionTitle: string): void {
    const codeContent = this.getDropdownCode(sectionTitle);
    navigator.clipboard.writeText(codeContent);
  }

  onSubmit() {
    console.log(this.testForm.value);
  }
}
