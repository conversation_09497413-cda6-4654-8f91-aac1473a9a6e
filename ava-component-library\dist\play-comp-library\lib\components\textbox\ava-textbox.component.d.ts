import { EventEmitter, ChangeDetectorRef, ElementRef, AfterViewInit } from '@angular/core';
import { ControlValueAccessor } from '@angular/forms';
import * as i0 from "@angular/core";
export type TextboxVariant = 'default' | 'primary' | 'success' | 'error' | 'warning' | 'info';
export type TextboxSize = 'sm' | 'md' | 'lg';
export type IconPosition = 'start' | 'end';
export type PersonalityTheme = 'minimal' | 'professional' | 'modern' | 'vibrant';
export type MetaphorIntensity = 0 | 10 | 25 | 50 | 75 | 100;
export declare class AvaTextboxComponent implements ControlValueAccessor, AfterViewInit {
    private cdr;
    label: string;
    placeholder: string;
    variant: TextboxVariant;
    size: TextboxSize;
    disabled: boolean;
    readonly: boolean;
    error: string;
    helper: string;
    icon: string;
    iconPosition: IconPosition;
    iconColor: string;
    id: string;
    name: string;
    autocomplete: string;
    type: string;
    maxlength?: number;
    minlength?: number;
    required: boolean;
    fullWidth: boolean;
    style?: Record<string, string>;
    personality?: PersonalityTheme;
    glassIntensity?: MetaphorIntensity;
    lightIntensity?: MetaphorIntensity;
    liquidIntensity?: MetaphorIntensity;
    gradientIntensity?: MetaphorIntensity;
    enableMetaphors: boolean;
    respectsGlobalPersonality: boolean;
    metaphor: string | string[];
    textboxBlur: EventEmitter<Event>;
    textboxFocus: EventEmitter<Event>;
    textboxInput: EventEmitter<Event>;
    textboxChange: EventEmitter<Event>;
    iconStartClick: EventEmitter<Event>;
    iconEndClick: EventEmitter<Event>;
    prefixContainer: ElementRef;
    suffixContainer: ElementRef;
    iconStartContainer: ElementRef;
    iconEndContainer: ElementRef;
    value: string;
    isFocused: boolean;
    hasProjectedPrefix: boolean;
    hasProjectedSuffix: boolean;
    hasProjectedStartIcon: boolean;
    hasProjectedEndIcon: boolean;
    private onChange;
    private onTouched;
    constructor(cdr: ChangeDetectorRef);
    ngAfterViewInit(): void;
    private checkProjectedContent;
    writeValue(value: string): void;
    registerOnChange(fn: (value: string) => void): void;
    registerOnTouched(fn: () => void): void;
    setDisabledState(isDisabled: boolean): void;
    onInput(event: Event): void;
    onFocus(event: Event): void;
    onBlur(event: Event): void;
    onChange_(event: Event): void;
    onIconStartClick(event: Event): void;
    onIconEndClick(event: Event): void;
    onIconKeydown(event: KeyboardEvent, position: 'start' | 'end'): void;
    get hasError(): boolean;
    get hasHelper(): boolean;
    get hasIcon(): boolean;
    get inputId(): string;
    get errorId(): string;
    get helperId(): string;
    get ariaDescribedBy(): string;
    /**
     * Generate metaphor intensity classes based on props and personality
     */
    get metaphorClasses(): string[];
    /**
     * Generate legacy metaphor classes for backward compatibility
     */
    get legacyMetaphorClasses(): string[];
    get inputClasses(): string;
    get wrapperClasses(): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<AvaTextboxComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<AvaTextboxComponent, "ava-textbox", never, { "label": { "alias": "label"; "required": false; }; "placeholder": { "alias": "placeholder"; "required": false; }; "variant": { "alias": "variant"; "required": false; }; "size": { "alias": "size"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "readonly": { "alias": "readonly"; "required": false; }; "error": { "alias": "error"; "required": false; }; "helper": { "alias": "helper"; "required": false; }; "icon": { "alias": "icon"; "required": false; }; "iconPosition": { "alias": "iconPosition"; "required": false; }; "iconColor": { "alias": "iconColor"; "required": false; }; "id": { "alias": "id"; "required": false; }; "name": { "alias": "name"; "required": false; }; "autocomplete": { "alias": "autocomplete"; "required": false; }; "type": { "alias": "type"; "required": false; }; "maxlength": { "alias": "maxlength"; "required": false; }; "minlength": { "alias": "minlength"; "required": false; }; "required": { "alias": "required"; "required": false; }; "fullWidth": { "alias": "fullWidth"; "required": false; }; "style": { "alias": "style"; "required": false; }; "personality": { "alias": "personality"; "required": false; }; "glassIntensity": { "alias": "glassIntensity"; "required": false; }; "lightIntensity": { "alias": "lightIntensity"; "required": false; }; "liquidIntensity": { "alias": "liquidIntensity"; "required": false; }; "gradientIntensity": { "alias": "gradientIntensity"; "required": false; }; "enableMetaphors": { "alias": "enableMetaphors"; "required": false; }; "respectsGlobalPersonality": { "alias": "respectsGlobalPersonality"; "required": false; }; "metaphor": { "alias": "metaphor"; "required": false; }; }, { "textboxBlur": "textboxBlur"; "textboxFocus": "textboxFocus"; "textboxInput": "textboxInput"; "textboxChange": "textboxChange"; "iconStartClick": "iconStartClick"; "iconEndClick": "iconEndClick"; }, never, ["[slot=prefix]", "[slot=icon-start]", "[slot=icon-end]", "[slot=suffix]"], true, never>;
}
