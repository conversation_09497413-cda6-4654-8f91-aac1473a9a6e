/**
 * =========================================================================
 * Play+ Design System: Radio Button Component Tokens
 *
 * Component-specific semantic tokens for radio button elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for radio button styling.
 * =========================================================================
 */

:root {
  /* --- Radio Base --- */
  --radio-size: 20px;
  --radio-border-radius: 50%;
  --radio-transition: var(--motion-pattern-fade);
  --radio-cursor: pointer;
  --radio-cursor-disabled: not-allowed;

  /* --- Radio Checkmark --- */
  --radio-checkmark-background: var(--color-background-primary);
  --radio-checkmark-background-disabled: var(--color-surface-disabled);
  --radio-checkmark-border: 2px solid var(--color-brand-primary);
  --radio-checkmark-border-hover: 2px solid var(--color-brand-primary-hover);
  --radio-checkmark-border-focus: 2px solid var(--color-brand-primary);
  --radio-checkmark-border-disabled: 2px solid var(--color-border-disabled);
  --radio-checkmark-border-radius: var(--radio-border-radius);

  /* --- Radio Checked State --- */
  --radio-checkmark-checked-background: var(--color-background-primary);
  --radio-checkmark-checked-border: 2px solid var(--color-brand-primary);
  --radio-checkmark-checked-border-hover: 2px solid var(--color-brand-primary-hover);
  --radio-checkmark-checked-border-focus: 2px solid var(--color-brand-primary);
  --radio-checkmark-checked-border-disabled: 2px solid var(--color-border-disabled);

  /* --- Radio Dot (Inner Circle) --- */
  --radio-dot-size: 8px;
  --radio-dot-background: var(--color-brand-primary);
  --radio-dot-background-disabled: var(--color-text-disabled);
  --radio-dot-border-radius: 50%;
  --radio-dot-position: center;

  /* --- Radio Label --- */
  --radio-label-font: var(--font-body-2);
  --radio-label-color: var(--color-text-primary);
  --radio-label-color-disabled: var(--color-text-disabled);
  --radio-label-margin-left: var(--global-spacing-3);
  --radio-label-cursor: pointer;
  --radio-label-cursor-disabled: not-allowed;

  /* --- Radio Sizes --- */
  --radio-size-sm: 16px;
  --radio-size-sm-dot: 6px;
  --radio-size-sm-label: var(--font-label);

  --radio-size-md: 20px;
  --radio-size-md-dot: 8px;
  --radio-size-md-label: var(--font-body-2);

  --radio-size-lg: 24px;
  --radio-size-lg-dot: 10px;
  --radio-size-lg-label: var(--font-body-1);

  /* --- Radio States --- */
  --radio-focus-ring: var(--accessibility-focus-ring-width) var(--accessibility-focus-ring-style) var(--accessibility-focus-ring-color);
  --radio-focus-ring-offset: var(--accessibility-focus-ring-offset);
  --radio-hover-transform: scale(1.05);
  --radio-active-transform: scale(0.95);

  /* --- Radio Group --- */
  --radio-group-gap: var(--global-spacing-4);
  --radio-group-direction: column;
  --radio-group-align-items: flex-start;
} 