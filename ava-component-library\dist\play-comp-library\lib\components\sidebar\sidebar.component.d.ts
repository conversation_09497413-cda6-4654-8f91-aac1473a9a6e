import { ChangeDetectorRef, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import * as i0 from "@angular/core";
export declare class SidebarComponent implements OnInit, OnChanges {
    private cdr;
    width: string;
    collapsedWidth: string;
    height: string;
    hoverAreaWidth: string;
    showCollapseButton: boolean;
    buttonVariant: 'inside' | 'outside';
    showHeader: boolean;
    showFooter: boolean;
    isCollapsed: boolean;
    collapseToggle: EventEmitter<boolean>;
    private _isCollapsed;
    constructor(cdr: ChangeDetectorRef);
    ngOnInit(): void;
    ngOnChanges(changes: SimpleChanges): void;
    toggleCollapse(): void;
    get sidebarWidth(): string;
    get collapsed(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<SidebarComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SidebarComponent, "ava-sidebar", never, { "width": { "alias": "width"; "required": false; }; "collapsedWidth": { "alias": "collapsedWidth"; "required": false; }; "height": { "alias": "height"; "required": false; }; "hoverAreaWidth": { "alias": "hoverAreaWidth"; "required": false; }; "showCollapseButton": { "alias": "showCollapseButton"; "required": false; }; "buttonVariant": { "alias": "buttonVariant"; "required": false; }; "showHeader": { "alias": "showHeader"; "required": false; }; "showFooter": { "alias": "showFooter"; "required": false; }; "isCollapsed": { "alias": "isCollapsed"; "required": false; }; }, { "collapseToggle": "collapseToggle"; }, never, ["[slot=header]", "[slot=content]", "[slot=footer]"], true, never>;
}
