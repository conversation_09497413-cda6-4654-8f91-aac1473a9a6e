{"name": "@ava/play-comp-library", "version": "1.0.31", "description": "Angular component library with reusable UI components", "author": "Ascendion", "peerDependencies": {"@angular/common": "^19.1.0", "@angular/core": "^19.1.0"}, "dependencies": {"tslib": "^2.3.0"}, "sideEffects": false, "publishConfig": {"access": "restricted", "registry": "https://pkgs.dev.azure.com/ascendionava/_packaging/ava-platform/npm/registry/"}, "repository": {"type": "git", "url": "git+https://dev.azure.com/ascendionava/ava-platform/_git/ava-component-library"}, "keywords": ["angular", "components", "ui-library"], "license": "MIT", "module": "fesm2022/ava-play-comp-library.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/ava-play-comp-library.mjs"}}}