/**
 * Component: Popup
 * Purpose: Popup component tokens for modal dialogs and overlays
 */

:root {
  /* Popup Base */
  --popup-background: var(--color-background-primary);
  --popup-border: 1px solid var(--color-border-default);
  --popup-border-radius: var(--global-radius-lg);
  --popup-shadow: var(--global-elevation-03);
  --popup-padding: var(--global-spacing-4);
  --popup-max-width: 32rem;
  --popup-max-height: 80vh;
  --popup-z-index: 1000;

  /* Popup Black color */
  --popup-black-color: var(--color-text-primary);

  /* Popup Overlay */
  --popup-overlay-background: rgba(0, 0, 0, 0.5);
  --popup-overlay-z-index: 999;

  /* Popup Content Padding */
  --popup-content-padding-xl: var(--global-spacing-5);
  --popup-content-gap: var(--global-spacing-3);

  /* Popup Header */
  --popup-header-background: var(--color-background-primary);
  --popup-header-padding: var(--global-spacing-4);
  --popup-header-border-bottom: 1px solid var(--color-border-subtle);
  --popup-header-border-radius: var(--global-radius-lg) var(--global-radius-lg) 0 0;

  --popup-heading-text: var(--color-text-primary);
  --popup-heading-font: var(--font-heading-h4);
  --popup-heading-size: var(--global-font-size-lg);
  --popup-heading-weight: var(--global-font-weight-semibold);
  --popup-heading-line-height: var(--global-line-height-tight);
  --popup-heading-margin: 0;

  /* Popup Content */
  --popup-content-background: var(--color-background-primary);
  --popup-content-padding: var(--global-spacing-4);
  --popup-content-border-radius: 0 0 var(--global-radius-lg) var(--global-radius-lg);

  --popup-description-text: var(--color-text-secondary);
  --popup-description-font: var(--font-body-1);
  --popup-description-size: var(--global-font-size-md);
  --popup-description-weight: var(--global-font-weight-regular);
  --popup-description-line-height: var(--global-line-height-normal);
  --popup-description-margin: var(--global-spacing-2) 0 0 0;

  /* Popup Menu */
  --popup-menu-background: var(--color-background-primary);
  --popup-menu-text: var(--color-text-primary);
  --popup-menu-font: var(--font-body-1);
  --popup-menu-size: var(--global-font-size-md);
  --popup-menu-weight: var(--global-font-weight-regular);
  --popup-menu-padding: var(--global-spacing-3);
  --popup-menu-border-radius: var(--global-radius-sm);

  --popup-menu-item-background: transparent;
  --popup-menu-item-text: var(--color-text-primary);
  --popup-menu-item-font: var(--font-body-2);
  --popup-menu-item-padding: var(--global-spacing-3);
  --popup-menu-item-border-radius: var(--global-radius-sm);
  --popup-menu-item-transition: all var(--global-motion-duration-standard) var(--global-motion-easing-standard);

  --popup-menu-item-hover-background: var(--color-surface-subtle-hover);
  --popup-menu-item-hover-text: var(--color-text-primary);

  --popup-menu-item-active-background: var(--color-surface-subtle);
  --popup-menu-item-active-text: var(--color-text-primary);

  --popup-menu-item-selected-background: var(--color-surface-interactive-default);
  --popup-menu-item-selected-text: var(--color-text-on-brand);

  --popup-menu-item-disabled-background: var(--color-background-disabled);
  --popup-menu-item-disabled-text: var(--color-text-disabled);
  --popup-menu-item-disabled-cursor: not-allowed;

  /* Popup Footer */
  --popup-footer-background: var(--color-background-primary);
  --popup-footer-padding: var(--global-spacing-4);
  --popup-footer-border-top: 1px solid var(--color-border-subtle);
  --popup-footer-border-radius: 0 0 var(--global-radius-lg) var(--global-radius-lg);
  --popup-footer-gap: var(--global-spacing-3);

  /* Popup Close Button */
  --popup-close-button-background: transparent;
  --popup-close-button-text: var(--color-text-secondary);
  --popup-close-button-size: var(--global-icon-size-md);
  --popup-close-button-border-radius: var(--global-radius-circle);
  --popup-close-button-padding: var(--global-spacing-2);
  --popup-close-button-transition: all var(--global-motion-duration-standard) var(--global-motion-easing-standard);

  --popup-close-button-hover-background: var(--color-surface-subtle-hover);
  --popup-close-button-hover-text: var(--color-text-primary);

  --popup-close-button-active-background: var(--color-surface-subtle);
  --popup-close-button-active-text: var(--color-text-primary);

  --popup-close-button-focus-background: transparent;
  --popup-close-button-focus-text: var(--color-text-secondary);
  --popup-close-button-focus-outline: 2px solid var(--accessibility-focus-ring-color);
  --popup-close-button-focus-outline-offset: 0.125rem;

  /* Popup Sizes */
  --popup-size-sm-max-width: 24rem;
  --popup-size-sm-padding: var(--global-spacing-3);
  --popup-size-sm-heading-size: var(--global-font-size-md);
  --popup-size-sm-description-size: var(--global-font-size-sm);

  --popup-size-md-max-width: 32rem;
  --popup-size-md-padding: var(--global-spacing-4);
  --popup-size-md-heading-size: var(--global-font-size-lg);
  --popup-size-md-description-size: var(--global-font-size-md);

  --popup-size-lg-max-width: 48rem;
  --popup-size-lg-padding: var(--global-spacing-6);
  --popup-size-lg-heading-size: var(--global-font-size-xl);
  --popup-size-lg-description-size: var(--global-font-size-lg);

  --popup-size-full-max-width: 100vw;
  --popup-size-full-max-height: 100vh;
  --popup-size-full-border-radius: 0;
  --popup-size-full-padding: var(--global-spacing-6);

  /* Popup Variants */
  --popup-variant-default-background: var(--color-background-primary);
  --popup-variant-default-border: 1px solid var(--color-border-default);
  --popup-variant-default-shadow: var(--global-elevation-03);

  --popup-variant-elevated-background: var(--color-background-primary);
  --popup-variant-elevated-border: none;
  --popup-variant-elevated-shadow: var(--global-elevation-04);

  --popup-variant-outlined-background: var(--color-background-primary);
  --popup-variant-outlined-border: 2px solid var(--color-border-default);
  --popup-variant-outlined-shadow: var(--global-elevation-02);

  /* Popup Positions */
  --popup-position-center-top: 50%;
  --popup-position-center-left: 50%;
  --popup-position-center-transform: translate(-50%, -50%);

  --popup-position-top-top: 1rem;
  --popup-position-top-left: 50%;
  --popup-position-top-transform: translateX(-50%);

  --popup-position-bottom-bottom: 1rem;
  --popup-position-bottom-left: 50%;
  --popup-position-bottom-transform: translateX(-50%);

  --popup-position-left-left: 1rem;
  --popup-position-left-top: 50%;
  --popup-position-left-transform: translateY(-50%);

  --popup-position-right-right: 1rem;
  --popup-position-right-top: 50%;
  --popup-position-right-transform: translateY(-50%);

  /* Popup Animation */
  --popup-animation-duration: var(--global-motion-duration-standard);
  --popup-animation-easing: var(--global-motion-easing-standard);
  --popup-animation-scale-initial: 0.95;
  --popup-animation-opacity-initial: 0;
  --popup-animation-scale-final: 1;
  --popup-animation-opacity-final: 1;
}