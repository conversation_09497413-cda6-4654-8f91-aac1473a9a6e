import { <PERSON><PERSON><PERSON><PERSON>, OnInit, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ChangeDetectorRef, SimpleChanges, OnChanges } from '@angular/core';
import * as i0 from "@angular/core";
export interface DropdownOption {
    name: string;
    value: string | number;
    icon?: string;
    disabled?: boolean;
}
export declare class DropdownComponent implements On<PERSON>nit, OnDestroy, OnChanges {
    private elementRef;
    private cdr;
    dropdownTitle: string;
    options: DropdownOption[];
    suboptions: {
        [key: string]: DropdownOption[];
    };
    checkboxOptions: string[];
    iconOptions: string[];
    search: boolean;
    enableSearch: boolean;
    selectedValue: string;
    singleSelect: boolean;
    dropdownIcon: string;
    disabled: boolean;
    label: string;
    required: boolean;
    selectionChange: EventEmitter<any>;
    valueChange: EventEmitter<any>;
    isOpen: boolean;
    searchTerm: string;
    selectedOptions: DropdownOption[];
    filteredOptions: DropdownOption[];
    expandedOption: string | null;
    showValidationError: boolean;
    hasBeenTouched: boolean;
    focusedOptionIndex: number;
    focusedSubOptionIndex: number;
    isNavigatingSubOptions: boolean;
    private pendingSelectedValue;
    private static allDropdowns;
    value: string;
    constructor(elementRef: ElementRef, cdr: ChangeDetectorRef);
    private onChange;
    private onTouched;
    writeValue(obj: any): void;
    registerOnChange(fn: any): void;
    registerOnTouched(fn: any): void;
    ngOnInit(): void;
    ngOnChanges(changes: SimpleChanges): void;
    ngOnDestroy(): void;
    private static closeAllDropdownsExcept;
    toggleDropdown(): void;
    closeDropdown(): void;
    private handleValidation;
    private clearValidationError;
    private resetFocusStates;
    onSearch(): void;
    selectOption(option: DropdownOption): void;
    handleCheckboxSelection(option: DropdownOption): void;
    selectSubOption(subOption: DropdownOption): void;
    toggleSubOptions(optionName: string): void;
    isOptionSelected(option: DropdownOption): boolean;
    isSubOptionSelected(subOption: DropdownOption): boolean;
    hasSubOptions(optionName: string): boolean;
    getOptionIcon(option: DropdownOption): string;
    shouldShowIcon(option: DropdownOption): boolean;
    isOptionDisabled(option: DropdownOption): boolean;
    isSubOptionDisabled(subOption: DropdownOption): boolean;
    getDisplayText(): string;
    private updateSelectedOptionsFromValue;
    emitChanges(): void;
    onDocumentClick(event: Event): void;
    onToggleClick(event: Event): void;
    onToggleKeyDown(event: KeyboardEvent): void;
    onDropdownKeyDown(event: KeyboardEvent): void;
    onSearchKeyDown(event: KeyboardEvent): void;
    onOptionKeyDown(event: KeyboardEvent, option: DropdownOption): void;
    onSubOptionKeyDown(event: KeyboardEvent, subOption: DropdownOption): void;
    private handleToggleActivation;
    private openAndFocus;
    private handleOptionActivation;
    private expandSubOptions;
    private navigateOptions;
    private navigateSubOptions;
    private focusOption;
    private focusSubOption;
    private focusElement;
    private focusSearchInput;
    private focusToggle;
    getSuboptionPosition(optionIndex: number): number;
    static ɵfac: i0.ɵɵFactoryDeclaration<DropdownComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<DropdownComponent, "ava-dropdown", never, { "dropdownTitle": { "alias": "dropdownTitle"; "required": false; }; "options": { "alias": "options"; "required": false; }; "suboptions": { "alias": "suboptions"; "required": false; }; "checkboxOptions": { "alias": "checkboxOptions"; "required": false; }; "iconOptions": { "alias": "iconOptions"; "required": false; }; "search": { "alias": "search"; "required": false; }; "enableSearch": { "alias": "enableSearch"; "required": false; }; "selectedValue": { "alias": "selectedValue"; "required": false; }; "singleSelect": { "alias": "singleSelect"; "required": false; }; "dropdownIcon": { "alias": "dropdownIcon"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "label": { "alias": "label"; "required": false; }; "required": { "alias": "required"; "required": false; }; }, { "selectionChange": "selectionChange"; "valueChange": "valueChange"; }, never, never, true, never>;
}
