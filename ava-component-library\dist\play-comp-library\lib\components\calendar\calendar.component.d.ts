import { EventEmitter, OnDestroy, OnInit, ElementRef } from '@angular/core';
import * as i0 from "@angular/core";
export interface DateRange {
    start: Date | null;
    end: Date | null;
}
export interface CalendarDay {
    date: Date;
    isCurrentMonth: boolean;
    isToday: boolean;
    isSelected: boolean;
    isInRange: boolean;
    isRangeStart: boolean;
    isRangeEnd: boolean;
}
export declare class CalendarComponent implements OnInit, OnDestroy {
    isRange: boolean;
    selectedDate: Date | null;
    dateRange: DateRange;
    weekdayFormat: 1 | 2 | 3;
    alwaysOpen: boolean;
    dateSelected: EventEmitter<Date>;
    rangeSelected: EventEmitter<DateRange>;
    dayInput: ElementRef<HTMLInputElement>;
    monthInput: ElementRef<HTMLInputElement>;
    yearInput: ElementRef<HTMLInputElement>;
    startDayInput: ElementRef<HTMLInputElement>;
    startMonthInput: ElementRef<HTMLInputElement>;
    startYearInput: ElementRef<HTMLInputElement>;
    endDayInput: ElementRef<HTMLInputElement>;
    endMonthInput: ElementRef<HTMLInputElement>;
    endYearInput: ElementRef<HTMLInputElement>;
    isOpen: boolean;
    currentMonth: number;
    currentYear: number;
    hoverDate: Date | null;
    isSelectingRangeEnd: boolean;
    currentSegment: string;
    selectedNavigation: 'month' | 'year';
    dayValue: string;
    monthValue: string;
    yearValue: string;
    startDayValue: string;
    startMonthValue: string;
    startYearValue: string;
    endDayValue: string;
    endMonthValue: string;
    endYearValue: string;
    readonly monthNames: string[];
    private readonly weekDaysBase;
    private readonly today;
    value: Date | DateRange | null;
    private onChange;
    private onTouched;
    get weekDays(): string[];
    get yearRange(): number[];
    get calendarDays(): CalendarDay[];
    ngOnInit(): void;
    ngOnDestroy(): void;
    writeValue(obj: any): void;
    registerOnChange(fn: any): void;
    registerOnTouched(fn: any): void;
    setDisabledState?(isDisabled: boolean): void;
    toggle(): void;
    close(): void;
    navigate(direction: number): void;
    selectNavigation(type: 'month' | 'year'): void;
    onMonthYearKeyDown(event: KeyboardEvent, type: 'month' | 'year'): void;
    getNavLabel(direction: number): string;
    selectDate(date: Date): void;
    onDayHover(date: Date): void;
    onDayLeave(): void;
    onSegmentFocus(segment: string): void;
    onSegmentBlur(segment: string): void;
    onKeyDown(event: KeyboardEvent, segment: string): void;
    onInputClick(): void;
    onDayInput(event: any): void;
    onMonthInput(event: any): void;
    onYearInput(event: any): void;
    onStartDayInput(event: any): void;
    onStartMonthInput(event: any): void;
    onStartYearInput(event: any): void;
    onEndDayInput(event: any): void;
    onEndMonthInput(event: any): void;
    onEndYearInput(event: any): void;
    onDocumentClick(event: Event): void;
    trackByDate(_index: number, day: CalendarDay): string;
    getDayClasses(day: CalendarDay): string;
    formatDate(date: Date | null): string;
    formatDateRange(): string;
    private limitValue;
    private focusNextSegment;
    private checkAutoAdvance;
    private validateAndUpdateDate;
    private validateSingleDate;
    private validateRangeDate;
    private navigateMonth;
    private navigateYear;
    private handleSingleSelection;
    private handleRangeSelection;
    private startNewRange;
    private completeRange;
    private updateStructuredInputFromDate;
    private updateStructuredRangeInputFromDates;
    private updateInputValues;
    private parseDate;
    private isValidDate;
    private toDateString;
    private isSameDay;
    private isDateSelected;
    private isDateInRange;
    private isDateRangeStart;
    private isDateRangeEnd;
    private isClickInside;
    static ɵfac: i0.ɵɵFactoryDeclaration<CalendarComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<CalendarComponent, "ava-calendar", never, { "isRange": { "alias": "isRange"; "required": false; }; "selectedDate": { "alias": "selectedDate"; "required": false; }; "dateRange": { "alias": "dateRange"; "required": false; }; "weekdayFormat": { "alias": "weekdayFormat"; "required": false; }; "alwaysOpen": { "alias": "alwaysOpen"; "required": false; }; }, { "dateSelected": "dateSelected"; "rangeSelected": "rangeSelected"; }, never, never, true, never>;
}
