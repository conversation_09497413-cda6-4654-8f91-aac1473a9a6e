.radio-group {
    display: flex;
    gap: var(--radio-group-gap);
    flex-direction: column;

    &[data-orientation='horizontal'] {
        flex-direction: row;
        align-items: center;
    }

    &[data-orientation='vertical'] {
        flex-direction: column;
        align-items: flex-start;
    }
}

.radio-wrapper {
    display: flex;
    align-items: center;
    position: relative;
    cursor: var(--radio-cursor);

    .radio-input {
        display: none;
    }

    .custom-radio {
        display: inline-block;
        position: relative;
        background-color: var(--radio-checkmark-background);
        border: var(--radio-checkmark-border);
        border-radius: var(--radio-checkmark-border-radius);
        flex-shrink: 0;
        transition: box-shadow 0.3s, border-color 0.3s, background 0.3s;

        &.disabled {
            background-color: var(--radio-checkmark-background-disabled) !important;
            border: var(--radio-checkmark-border-disabled) !important;
            cursor: var(--radio-cursor-disabled);
        }

        &[data-size='small'] {
            width: var(--radio-size-sm);
            height: var(--radio-size-sm);
        }

        &[data-size='medium'] {
            width: var(--radio-size-md);
            height: var(--radio-size-md);
        }

        &[data-size='large'] {
            width: var(--radio-size-lg);
            height: var(--radio-size-lg);
        }

        &.animated-shadow {
            transition: box-shadow 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            box-shadow: 0 0 0 8px rgba(0, 0, 0, 0.10), 0 2px 8px 0 rgba(0, 0, 0, 0.10);
        }
    }

    .radio-dot {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: var(--radio-dot-background);
        border-radius: var(--radio-dot-border-radius);
    }

    &[data-size='small'] .custom-radio {
        width: var(--radio-size-sm);
        height: var(--radio-size-sm);
    }

    &[data-size='small'] .radio-dot {
        width: var(--radio-size-sm-dot);
        height: var(--radio-size-sm-dot);
    }

    &[data-size='medium'] .custom-radio {
        width: var(--radio-size-md);
        height: var(--radio-size-md);
    }

    &[data-size='medium'] .radio-dot {
        width: var(--radio-size-md-dot);
        height: var(--radio-size-md-dot);
    }

    &[data-size='large'] .custom-radio {
        width: var(--radio-size-lg);
        height: var(--radio-size-lg);
    }

    &[data-size='large'] .radio-dot {
        width: var(--radio-size-lg-dot);
        height: var(--radio-size-lg-dot);
    }

    .radio-label {
        margin-left: var(--radio-label-margin-left);
        color: var(--radio-label-color);
        font: var(--radio-label-font);

        &.disabled {
            color: var(--radio-label-color-disabled) !important;
            cursor: var(--radio-label-cursor-disabled);
        }
    }

    &[data-size='small'] .radio-label {
        font: var(--radio-size-sm-label);
    }

    &[data-size='medium'] .radio-label {
        font: var(--radio-size-md-label);
    }

    &[data-size='large'] .radio-label {
        font: var(--radio-size-lg-label);
    }
}