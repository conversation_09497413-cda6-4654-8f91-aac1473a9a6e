import * as i0 from "@angular/core";
export type BadgeState = 'high-priority' | 'medium-priority' | 'low-priority' | 'neutral' | 'information';
export type BadgeSize = 'large' | 'medium' | 'small';
export declare class BadgesComponent {
    state: BadgeState;
    size: BadgeSize;
    count?: number;
    iconName?: string;
    iconColor?: string;
    iconSize?: number;
    get displayCount(): string;
    get badgeClasses(): string;
    get hasContent(): boolean;
    get isSingleDigit(): boolean;
    onKeyPress(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<BadgesComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<BadgesComponent, "ava-badges", never, { "state": { "alias": "state"; "required": false; }; "size": { "alias": "size"; "required": false; }; "count": { "alias": "count"; "required": false; }; "iconName": { "alias": "iconName"; "required": false; }; "iconColor": { "alias": "iconColor"; "required": false; }; "iconSize": { "alias": "iconSize"; "required": false; }; }, {}, never, never, true, never>;
}
