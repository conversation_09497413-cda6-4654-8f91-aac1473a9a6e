import { ElementRef, EventEmitter, WritableSignal } from '@angular/core';
import { ControlValueAccessor } from '@angular/forms';
import * as i0 from "@angular/core";
export declare class SliderComponent implements ControlValueAccessor {
    private elementRef;
    min: number;
    max: number;
    value: number;
    step: number;
    showTooltip: boolean;
    valueChange: EventEmitter<number>;
    sliderTrack: ElementRef;
    isHovered: boolean;
    isDragging: boolean;
    private onChange;
    private onTouched;
    decimalStepValue: WritableSignal<number>;
    constructor(elementRef: ElementRef);
    get percentage(): number;
    writeValue(value: number): void;
    registerOnChange(fn: any): void;
    registerOnTouched(fn: any): void;
    onTrackClick(event: MouseEvent): void;
    startDrag(event: MouseEvent): void;
    onKeyDown(event: KeyboardEvent): void;
    onMouseMove(event: MouseEvent): void;
    onMouseUp(): void;
    onDecimalStepChange(value: number): void;
    private updateValueFromEvent;
    private updateValue;
    static ɵfac: i0.ɵɵFactoryDeclaration<SliderComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SliderComponent, "ava-slider", never, { "min": { "alias": "min"; "required": false; }; "max": { "alias": "max"; "required": false; }; "value": { "alias": "value"; "required": false; }; "step": { "alias": "step"; "required": false; }; "showTooltip": { "alias": "showTooltip"; "required": false; }; }, { "valueChange": "valueChange"; }, never, never, true, never>;
}
