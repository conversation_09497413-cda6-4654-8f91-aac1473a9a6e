import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
/**
 * AvaTagComponent: Modern, accessible, and highly customizable tag/chip component.
 * Supports filled/outlined, color variants, pill/rect, removable, icons, avatars, sizes, and custom styles.
 */
export declare class AvaTagComponent {
    /** Tag label text */
    label: string;
    /** Color variant */
    color: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' | 'custom';
    /** Outlined or filled */
    variant: 'filled' | 'outlined';
    /** Tag size */
    size: 'sm' | 'md' | 'lg';
    /** Pill/rounded shape */
    pill: boolean;
    /** Removable (shows close icon) */
    removable: boolean;
    /** Disabled state */
    disabled: boolean;
    /** Icon name (ava-icon) */
    icon?: string;
    /** Icon position (left only, close always right) */
    iconPosition: 'start' | 'end';
    /** Avatar: image URL or initials */
    avatar?: string;
    /** Custom style object for CSS vars */
    customStyle?: Record<string, string>;
    /** Custom class for tag */
    customClass?: string;
    /** Custom icon color */
    iconColor?: string;
    /** Emits when tag is removed (close icon) */
    removed: EventEmitter<void>;
    /** Emits when tag is clicked (if handler provided) */
    clicked: EventEmitter<void>;
    /** True if tag is clickable (handler attached and not disabled) */
    get clickable(): boolean;
    /** Remove handler (close icon) */
    onRemove(event: Event): void;
    /** Click handler (entire tag) */
    onClick(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<AvaTagComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<AvaTagComponent, "ava-tag", never, { "label": { "alias": "label"; "required": false; }; "color": { "alias": "color"; "required": false; }; "variant": { "alias": "variant"; "required": false; }; "size": { "alias": "size"; "required": false; }; "pill": { "alias": "pill"; "required": false; }; "removable": { "alias": "removable"; "required": false; }; "disabled": { "alias": "disabled"; "required": false; }; "icon": { "alias": "icon"; "required": false; }; "iconPosition": { "alias": "iconPosition"; "required": false; }; "avatar": { "alias": "avatar"; "required": false; }; "customStyle": { "alias": "customStyle"; "required": false; }; "customClass": { "alias": "customClass"; "required": false; }; "iconColor": { "alias": "iconColor"; "required": false; }; }, { "removed": "removed"; "clicked": "clicked"; }, never, never, true, never>;
}
