/**
 * Component: Calendar
 * Purpose: Calendar component tokens for date selection and display
 */

:root {
  /* Calendar Base */
  --calendar-background: var(--color-background-primary);
  --calendar-border: 1px solid var(--color-border-default);
  --calendar-border-radius: var(--global-radius-md);
  --calendar-font-family: var(--font-family-body);
  --calendar-shadow: var(--global-elevation-02);

  /* Calendar Header */
  --calendar-header-background: var(--color-background-secondary);
  --calendar-header-text: var(--color-text-primary);
  --calendar-header-font: var(--font-heading-h4);
  --calendar-header-padding: var(--global-spacing-4);
  --calendar-header-border-bottom: 1px solid var(--color-border-subtle);

  /* Calendar Navigation */
  --calendar-nav-button-background: var(--color-surface-subtle);
  --calendar-nav-button-text: var(--color-text-secondary);
  --calendar-nav-button-hover-background: var(--color-surface-subtle-hover);
  --calendar-nav-button-size: var(--global-icon-size-md);
  --calendar-nav-button-border-radius: var(--global-radius-sm);

  /* Calendar Month/Year Display */
  --calendar-month-text: var(--color-text-primary);
  --calendar-year-text: var(--color-text-secondary);
  --calendar-month-font: var(--font-heading-h4);
  --calendar-year-font: var(--font-body-2);

  /* Calendar Grid */
  --calendar-grid-background: var(--color-background-primary);
  --calendar-grid-border: 1px solid var(--color-border-subtle);
  --calendar-cell-padding: var(--global-spacing-2);
  --calendar-cell-text: var(--color-text-primary);
  --calendar-cell-font: var(--font-body-2);
  --calendar-cell-border-radius: var(--global-radius-sm);

  /* Calendar Day Names */
  --calendar-day-name-text: var(--color-text-secondary);
  --calendar-day-name-font: var(--font-label);
  --calendar-day-name-background: var(--color-background-secondary);

  /* Calendar Date States */
  --calendar-date-text: var(--color-text-primary);
  --calendar-date-hover-background: var(--color-surface-subtle-hover);
  --calendar-date-hover-text: var(--color-text-primary);

  --calendar-date-selected-background: var(--color-surface-interactive-default);
  --calendar-date-selected-text: var(--color-text-on-brand);

  --calendar-date-today-background: var(--color-surface-subtle);
  --calendar-date-today-text: var(--color-text-primary);
  --calendar-date-today-border: 2px solid var(--color-border-interactive);

  --calendar-date-disabled-text: var(--color-text-disabled);
  --calendar-date-disabled-background: var(--color-background-disabled);

  /* Calendar Range Selection */
  --calendar-range-start-background: var(--color-surface-interactive-default);
  --calendar-range-start-text: var(--color-text-on-brand);
  --calendar-range-end-background: var(--color-surface-interactive-default);
  --calendar-range-end-text: var(--color-text-on-brand);
  --calendar-range-in-between-background: var(--color-surface-subtle);
  --calendar-range-in-between-text: var(--color-text-primary);

  /* Calendar Sizes */
  --calendar-size-sm-font: var(--global-font-size-xs);
  --calendar-size-sm-padding: var(--global-spacing-2);
  --calendar-size-sm-cell-padding: var(--global-spacing-1);

  --calendar-size-md-font: var(--global-font-size-sm);
  --calendar-size-md-padding: var(--global-spacing-3);
  --calendar-size-md-cell-padding: var(--global-spacing-2);

  --calendar-size-lg-font: var(--global-font-size-md);
  --calendar-size-lg-padding: var(--global-spacing-4);
  --calendar-size-lg-cell-padding: var(--global-spacing-3);

  /* Calendar Input */
  --calendar-input-background: var(--color-background-primary);
  --calendar-input-border: 1px solid var(--color-border-default);
  --calendar-input-border-radius: var(--global-radius-md);
  --calendar-input-text: var(--color-text-primary);
  --calendar-input-font: var(--font-body-1);
  --calendar-input-padding: var(--global-spacing-3);
  --calendar-input-focus-border: 1px solid var(--color-border-focus);
  --calendar-input-focus-shadow: 0 0 0 2px var(--accessibility-focus-ring-color);

  /* Calendar Popup */
  --calendar-popup-background: var(--color-background-primary);
  --calendar-popup-border: 1px solid var(--color-border-default);
  --calendar-popup-border-radius: var(--global-radius-lg);
  --calendar-popup-shadow: var(--global-elevation-03);
  --calendar-popup-z-index: 1000;

  /* Calendar Icon */
  --calendar-icon-background: var(--color-surface-subtle);
  --calendar-icon-color: var(--color-text-secondary);
  --calendar-icon-size: var(--global-icon-size-md);
  --calendar-icon-border-radius: var(--global-radius-sm);
} 