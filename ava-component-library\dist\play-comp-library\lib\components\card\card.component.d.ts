import * as i0 from "@angular/core";
export declare class CardComponent {
    heading: string;
    content: string;
    static ɵfac: i0.ɵɵFactoryDeclaration<CardComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<CardComponent, "ava-card", never, { "heading": { "alias": "heading"; "required": false; }; "content": { "alias": "content"; "required": false; }; }, {}, never, ["div[header]", "div[content]", "div[contentFooter]", "div[footer]"], true, never>;
}
