/**
 * =========================================================================
 * Play+ Design System: Dark Theme
 *
 * Dark theme overrides for semantic tokens.
 * This theme maintains the same token structure but changes
 * the visual appearance to a dark mode.
 * =========================================================================
 */

[data-theme="dark"] {
  /* --- Dark Theme Color Overrides --- */
  --color-brand-primary: var(--global-color-pink-500);
  --color-brand-secondary: var(--global-color-purple-500);
  --color-text-primary: var(--global-color-gray-100);
  --color-text-secondary: var(--global-color-gray-300);
  --color-text-placeholder: var(--global-color-gray-400);
  --color-text-disabled: var(--global-color-gray-400);
  --color-text-on-brand: var(--global-color-white);
  --color-text-interactive: var(--global-color-pink-500);
  --color-text-interactive-hover: var(--global-color-rose-500);
  --color-text-success: var(--global-color-spearmint-500);
  --color-text-error: var(--global-color-rose-500);
  --color-background-primary: var(--global-color-black);
  --color-background-secondary: var(--global-color-gray-700);
  --color-background-disabled: var(--global-color-gray-600);
  --color-surface-interactive-default: var(--global-color-pink-500);
  --color-surface-interactive-hover: var(--global-color-rose-500);
  --color-surface-interactive-active: var(--global-color-rose-700);
  --color-surface-disabled: var(--global-color-gray-600);
  --color-surface-subtle-hover: var(--global-color-gray-700);
  --color-border-default: var(--global-color-gray-600);
  --color-border-subtle: var(--global-color-gray-700);
  --color-border-interactive: var(--global-color-pink-500);
  --color-border-focus: var(--global-color-pink-500);
  --color-border-error: var(--global-color-rose-500);

  /* --- Dark Theme Glassmorphism --- */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(0, 0, 0, 0.25);
  --glass-border-color: rgba(255, 255, 255, 0.1);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-03);

  /* Semantic Border Colors */
  --color-border-success: var(--global-color-green-500 );

  /* =======================
     DARK THEME: RGB OVERRIDES
     Extract RGB values for dark theme effect colors
     ======================= */
  --rgb-brand-primary: 233, 30, 99;        /* From #e91e63 */
  --rgb-brand-secondary: 156, 39, 176;     /* From #9c27b0 */
  --rgb-violet: 124, 58, 237;              /* From #7c3aed */
  --rgb-white: 255, 255, 255;              /* From #ffffff */
  --rgb-black: 0, 0, 0;                    /* From #000000 */

  /* =======================
     DARK THEME: EFFECT COLOR OVERRIDES
     Override effect colors for dark theme adaptation
     ======================= */
  --effect-color-primary: var(--rgb-brand-primary);      /* Pink for dark theme */
  --effect-color-secondary: var(--rgb-brand-secondary);  /* Purple for dark theme */
  --effect-color-accent: var(--rgb-violet);              /* Violet accent */
  --effect-color-neutral: var(--rgb-white);              /* White shadows on dark bg */
  --effect-color-surface: var(--rgb-black);              /* Black glass/shimmer on dark bg */
}

/* =======================
   DARK THEME: PERSONALITY ACTIVATION SYSTEM
   How to use: <div data-theme="dark" data-personality="professional">
   ======================= */

/* === PERSONALITY ACTIVATION: Direct data-personality attribute === */

/* Minimal Personality in Dark Theme */
[data-theme="dark"][data-personality="minimal"] {
  --active-glass: var(--personality-minimal-glass);       /* glass-10 from base */
  --active-light: var(--personality-minimal-light);       /* ambient-25 from base */
  --active-motion: var(--personality-minimal-liquid);     /* elastic-10 from base */
  --active-duration: var(--personality-minimal-duration); /* liquid-duration-25 from base */
}

/* Professional Personality in Dark Theme */
[data-theme="dark"][data-personality="professional"] {
  --active-glass: var(--personality-professional-glass);     /* glass-25 from base */
  --active-light: var(--personality-professional-light);     /* glow-50 from base */
  --active-motion: var(--personality-professional-liquid);   /* ripple-25 from base */
  --active-duration: var(--personality-professional-duration); /* liquid-duration-50 from base */
}

/* Modern Personality in Dark Theme */
[data-theme="dark"][data-personality="modern"] {
  --active-glass: var(--personality-modern-glass);       /* glass-50 from base */
  --active-light: var(--personality-modern-light);       /* glow-75 from base */
  --active-motion: var(--personality-modern-liquid);     /* elastic-50 from base */
  --active-duration: var(--personality-modern-duration); /* liquid-duration-75 from base */
}

/* Vibrant Personality in Dark Theme */
[data-theme="dark"][data-personality="vibrant"] {
  --active-glass: var(--personality-vibrant-glass);       /* glass-75 from base */
  --active-light: var(--personality-vibrant-light);       /* neon-glow-100 from base */
  --active-motion: var(--personality-vibrant-liquid);     /* splash-75 from base */
  --active-duration: var(--personality-vibrant-duration); /* liquid-duration-100 from base */
}

