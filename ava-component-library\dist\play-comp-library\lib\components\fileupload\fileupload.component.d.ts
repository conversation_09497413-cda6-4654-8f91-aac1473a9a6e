import { EventEmitter, OnInit } from '@angular/core';
import * as i0 from "@angular/core";
export declare class FileUploadComponent implements OnInit {
    fileUploaded: EventEmitter<File>;
    filesListChanged: EventEmitter<File[]>;
    theme: 'light' | 'dark';
    uploaderId: string;
    enableAnimation: boolean;
    allowedFormats: string[];
    singleFileMode: boolean;
    maxFiles: number | null;
    componentTitle: string;
    supportedFormatLabel: string;
    maxFileSize: number;
    appConstants: {
        UPLOAD_TITLE: string;
        CLOSE_UPLOAD_DIALOG: string;
        SUPPORTED_FORMATS_LABEL: string;
        SUPPORTED_FORMATS_LIST: string;
        CLICK_TO_UPLOAD: string;
        FILE_UPLOADED_SUCCESS: string;
        CLICK_TO_REUPLOAD: string;
        INVALID_FILE_TYPE: string;
        FILE_SIZE_ERROR: string;
        UPLOAD_BUTTON: string;
        FILE_UPLOAD_PROMPT: string;
        MAX_FILE_SIZE: number;
        ALLOWED_EXTENSIONS: string[];
        ACCEPTED_FILE_TYPES: string;
        MAX_FILES_ERROR: string;
        PHONE_REQUIRED_ERROR: string;
        PHONE_FORMAT_ERROR: string;
        AUTHENTICATION_TITLE: string;
        SENT_TITLE: string;
        VERIFICATION_SUCCESS_TITLE: string;
        NEED_TO_VERIFY: string;
        CANCEL_BUTTON: string;
        PROCEED_TO_VERIFY_BUTTON: string;
        USERNAME_PLACEHOLDER: string;
        USERNAME_REQUIRED_ERROR: string;
        ENTER_EMAIL: string;
        SEND_CODE_EMAIL: string;
        EMAIL_REQUIRED_ERROR: string;
        ENTER_PHONE: string;
        SEND_CODE_SMS: string;
        VERIFICATION_SENT_EMAIL: string;
        VERIFICATION_SENT_SMS: string;
        CLOSE_BUTTON: string;
        VERIFICATION_CODE_PLACEHOLDER: string;
        VERIFICATION_CODE_ERROR: string;
        PASSWORD_PLACEHOLDER: string;
        PASSWORD_ERROR: string;
        VERIFICATION_SUCCESS: string;
    };
    uploadedFiles: File[];
    fileUploadedSuccess: boolean;
    fileFormatError: boolean;
    fileSizeError: boolean;
    maxFilesError: boolean;
    isUploadActive: boolean;
    viewAll: boolean;
    uniqueId: string;
    ngOnInit(): void;
    get allowedFormatsList(): string[];
    toggleViewAll(): void;
    sizeFormat(bytes: number, decimals?: number): string;
    onFileSelected(event: Event): void;
    onDragOver(event: DragEvent): void;
    onDrop(event: DragEvent): void;
    private handleFile;
    openFileSelector(): void;
    uploadFile(): void;
    removeFile(index: number): void;
    resetUpload(): void;
    closeUpload(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<FileUploadComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<FileUploadComponent, "ava-file-upload", never, { "theme": { "alias": "theme"; "required": false; }; "uploaderId": { "alias": "uploaderId"; "required": false; }; "enableAnimation": { "alias": "enableAnimation"; "required": false; }; "allowedFormats": { "alias": "allowedFormats"; "required": false; }; "singleFileMode": { "alias": "singleFileMode"; "required": false; }; "maxFiles": { "alias": "maxFiles"; "required": false; }; "componentTitle": { "alias": "componentTitle"; "required": false; }; "supportedFormatLabel": { "alias": "supportedFormatLabel"; "required": false; }; "maxFileSize": { "alias": "maxFileSize"; "required": false; }; }, { "fileUploaded": "fileUploaded"; "filesListChanged": "filesListChanged"; }, never, never, true, never>;
}
