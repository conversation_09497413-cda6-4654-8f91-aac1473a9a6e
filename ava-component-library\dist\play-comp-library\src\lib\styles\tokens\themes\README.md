# Theme Tokens Documentation

## Overview

The `themes/` directory contains theme-specific design token overrides that modify the base semantic tokens to create different visual themes. This enables the Play+ Design System to support multiple themes while maintaining design consistency.

## Architecture

Theme tokens follow a simple override structure:

```
themes/
├── _light.css        # Light theme overrides
└── _dark.css         # Dark theme overrides
```

## Theme System

### Theme Token Structure

Theme files override semantic tokens to create different visual experiences:

```css
/**
 * Theme: [Theme Name]
 * Purpose: [Brief description of the theme's purpose]
 */

/* Theme-specific overrides */
[data-theme="theme-name"] {
  /* Override semantic tokens for this theme */
  --color-background-primary: var(--global-color-gray-900);
  --color-text-primary: var(--global-color-white);
  --color-border-default: var(--global-color-gray-700);
}
```

## Available Themes

### 1. Light Theme (`_light.css`)
The default light theme with bright backgrounds and dark text.

**Characteristics:**
- Light backgrounds
- Dark text
- Subtle shadows
- High contrast borders

**Key Overrides:**
```css
[data-theme="light"] {
  /* Background colors */
  --color-background-primary: var(--global-color-white);
  --color-background-secondary: var(--global-color-gray-50);
  
  /* Text colors */
  --color-text-primary: var(--global-color-gray-700);
  --color-text-secondary: var(--global-color-gray-600);
  
  /* Border colors */
  --color-border-default: var(--global-color-gray-300);
  --color-border-subtle: var(--global-color-gray-200);
  
  /* Surface colors */
  --color-surface-subtle: var(--global-color-gray-100);
  --color-surface-subtle-hover: var(--global-color-gray-200);
}
```

### 2. Dark Theme (`_dark.css`)
A dark theme with dark backgrounds and light text.

**Characteristics:**
- Dark backgrounds
- Light text
- Elevated shadows
- High contrast elements

**Key Overrides:**
```css
[data-theme="dark"] {
  /* Background colors */
  --color-background-primary: var(--global-color-gray-900);
  --color-background-secondary: var(--global-color-gray-800);
  
  /* Text colors */
  --color-text-primary: var(--global-color-white);
  --color-text-secondary: var(--global-color-gray-300);
  
  /* Border colors */
  --color-border-default: var(--global-color-gray-700);
  --color-border-subtle: var(--global-color-gray-600);
  
  /* Surface colors */
  --color-surface-subtle: var(--global-color-gray-800);
  --color-surface-subtle-hover: var(--global-color-gray-700);
}
```

## Theme Application

### CSS Implementation

Themes are applied using CSS custom properties and data attributes:

```css
/* Default theme (light) */
:root {
  /* Light theme tokens are applied by default */
}

/* Dark theme */
[data-theme="dark"] {
  /* Dark theme overrides */
}

/* Alternative class-based approach */
.theme-dark {
  /* Dark theme overrides */
}
```

### JavaScript Implementation

```javascript
// Apply theme
function setTheme(themeName) {
  document.documentElement.setAttribute('data-theme', themeName);
  localStorage.setItem('theme', themeName);
}

// Get current theme
function getCurrentTheme() {
  return document.documentElement.getAttribute('data-theme') || 'light';
}

// Initialize theme
function initializeTheme() {
  const savedTheme = localStorage.getItem('theme') || 'light';
  setTheme(savedTheme);
}

// Theme toggle
function toggleTheme() {
  const currentTheme = getCurrentTheme();
  const newTheme = currentTheme === 'light' ? 'dark' : 'light';
  setTheme(newTheme);
}
```

### React Implementation

```jsx
import { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext();

export function ThemeProvider({ children }) {
  const [theme, setTheme] = useState('light');

  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
  }, []);

  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  return useContext(ThemeContext);
}
```

## Theme Token Categories

### 1. Background Colors
Theme-specific background colors for different UI layers.

```css
--color-background-primary: var(--global-color-white); /* Light */
--color-background-primary: var(--global-color-gray-900); /* Dark */

--color-background-secondary: var(--global-color-gray-50); /* Light */
--color-background-secondary: var(--global-color-gray-800); /* Dark */
```

### 2. Text Colors
Theme-specific text colors for different content types.

```css
--color-text-primary: var(--global-color-gray-700); /* Light */
--color-text-primary: var(--global-color-white); /* Dark */

--color-text-secondary: var(--global-color-gray-600); /* Light */
--color-text-secondary: var(--global-color-gray-300); /* Dark */
```

### 3. Border Colors
Theme-specific border colors for different UI elements.

```css
--color-border-default: var(--global-color-gray-300); /* Light */
--color-border-default: var(--global-color-gray-700); /* Dark */

--color-border-subtle: var(--global-color-gray-200); /* Light */
--color-border-subtle: var(--global-color-gray-600); /* Dark */
```

### 4. Surface Colors
Theme-specific surface colors for interactive elements.

```css
--color-surface-subtle: var(--global-color-gray-100); /* Light */
--color-surface-subtle: var(--global-color-gray-800); /* Dark */

--color-surface-subtle-hover: var(--global-color-gray-200); /* Light */
--color-surface-subtle-hover: var(--global-color-gray-700); /* Dark */
```

### 5. Elevation Colors
Theme-specific elevation and shadow colors.

```css
--global-elevation-01: 0px 2px 4px rgba(0, 0, 0, 0.08); /* Light */
--global-elevation-01: 0px 2px 4px rgba(0, 0, 0, 0.3); /* Dark */
```

## Best Practices

### 1. Theme Consistency
- Maintain consistent color relationships across themes
- Ensure proper contrast ratios for accessibility
- Test all component states in both themes

### 2. Token Overrides
- Only override semantic tokens, never global tokens
- Maintain the same token structure across themes
- Use meaningful color relationships

### 3. Accessibility
- Ensure WCAG AA contrast ratios (4.5:1 for normal text)
- Test with color blindness simulators
- Provide sufficient visual distinction between elements

### 4. Performance
- Use CSS custom properties for efficient theme switching
- Avoid complex calculations in theme overrides
- Minimize the number of overridden tokens

## Adding New Themes

### 1. Create Theme File
```css
/**
 * Theme: [Theme Name]
 * Purpose: [Brief description]
 */

[data-theme="theme-name"] {
  /* Background colors */
  --color-background-primary: var(--global-color-[color]-[shade]);
  --color-background-secondary: var(--global-color-[color]-[shade]);
  
  /* Text colors */
  --color-text-primary: var(--global-color-[color]-[shade]);
  --color-text-secondary: var(--global-color-[color]-[shade]);
  
  /* Border colors */
  --color-border-default: var(--global-color-[color]-[shade]);
  --color-border-subtle: var(--global-color-[color]-[shade]);
  
  /* Surface colors */
  --color-surface-subtle: var(--global-color-[color]-[shade]);
  --color-surface-subtle-hover: var(--global-color-[color]-[shade]);
}
```

### 2. Add to Index
```css
/* In _index.css */
@import "./themes/_theme-name.css";
```

### 3. Update Documentation
- Document the theme's purpose and characteristics
- Provide usage examples
- Include accessibility considerations

## Testing Themes

### 1. Visual Testing
- Test all components in both themes
- Verify contrast ratios
- Check for visual inconsistencies

### 2. Accessibility Testing
- Use color contrast checkers
- Test with screen readers
- Verify keyboard navigation

### 3. Performance Testing
- Measure theme switching performance
- Test with large component trees
- Verify memory usage

## Examples

### Complete Theme Implementation
```css
/* Light theme */
[data-theme="light"] {
  /* Backgrounds */
  --color-background-primary: var(--global-color-white);
  --color-background-secondary: var(--global-color-gray-50);
  
  /* Text */
  --color-text-primary: var(--global-color-gray-700);
  --color-text-secondary: var(--global-color-gray-600);
  --color-text-placeholder: var(--global-color-gray-400);
  
  /* Borders */
  --color-border-default: var(--global-color-gray-300);
  --color-border-subtle: var(--global-color-gray-200);
  
  /* Surfaces */
  --color-surface-subtle: var(--global-color-gray-100);
  --color-surface-subtle-hover: var(--global-color-gray-200);
  
  /* Elevation */
  --global-elevation-01: 0px 2px 4px rgba(0, 0, 0, 0.08);
  --global-elevation-02: 0px 4px 12px rgba(0, 0, 0, 0.1);
}

/* Dark theme */
[data-theme="dark"] {
  /* Backgrounds */
  --color-background-primary: var(--global-color-gray-900);
  --color-background-secondary: var(--global-color-gray-800);
  
  /* Text */
  --color-text-primary: var(--global-color-white);
  --color-text-secondary: var(--global-color-gray-300);
  --color-text-placeholder: var(--global-color-gray-500);
  
  /* Borders */
  --color-border-default: var(--global-color-gray-700);
  --color-border-subtle: var(--global-color-gray-600);
  
  /* Surfaces */
  --color-surface-subtle: var(--global-color-gray-800);
  --color-surface-subtle-hover: var(--global-color-gray-700);
  
  /* Elevation */
  --global-elevation-01: 0px 2px 4px rgba(0, 0, 0, 0.3);
  --global-elevation-02: 0px 4px 12px rgba(0, 0, 0, 0.4);
}
```

### Theme Toggle Component
```jsx
function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <button onClick={toggleTheme} className="theme-toggle">
      {theme === 'light' ? '🌙' : '☀️'}
    </button>
  );
}
```

---

*This documentation is maintained as part of the Play+ Design System. For questions or contributions, please refer to the project guidelines.* 