import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>iew<PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef, AfterViewChecked } from '@angular/core';
import { ButtonComponent } from '../button/button.component';
import * as i0 from "@angular/core";
export interface AvaTabDropdownItem {
    label: string;
    value: string | number;
    icon?: string;
    iconColor?: string;
    subtitle?: string;
}
export interface AvaTab {
    label: string;
    value: string | number;
    icon?: string;
    iconPosition?: 'top' | 'bottom' | 'start' | 'end';
    disabled?: boolean;
    content?: string | null;
    id?: string;
    dropdown?: {
        items: AvaTabDropdownItem[];
    };
    iconColor?: string;
}
export declare class TabsComponent implements OnInit, AfterViewInit, AfterViewChecked, OnDestroy {
    private cdr;
    tabs: AvaTab[];
    value: string | number | null;
    valueChange: EventEmitter<string | number>;
    highlightActiveText: boolean;
    maxWidth?: string;
    showChevrons: boolean;
    ariaLabel?: string;
    variant: 'default' | 'button' | 'icon';
    style?: Record<string, string>;
    iconColor?: string;
    container: boolean;
    containerStyle?: Record<string, string>;
    dropdownSelect: EventEmitter<{
        parent: AvaTab;
        item: {
            label: string;
            value: string | number;
            icon?: string;
        };
    }>;
    tabClick: EventEmitter<AvaTab>;
    tabHover: EventEmitter<AvaTab>;
    tabFocus: EventEmitter<AvaTab>;
    tabBlur: EventEmitter<AvaTab>;
    dropdownItemClick: EventEmitter<{
        parent: AvaTab;
        item: {
            label: string;
            value: string | number;
            icon?: string;
        };
    }>;
    dropdownItemHover: EventEmitter<{
        parent: AvaTab;
        item: {
            label: string;
            value: string | number;
            icon?: string;
        };
    }>;
    dropdownItemFocus: EventEmitter<{
        parent: AvaTab;
        item: {
            label: string;
            value: string | number;
            icon?: string;
        };
    }>;
    dropdownItemBlur: EventEmitter<{
        parent: AvaTab;
        item: {
            label: string;
            value: string | number;
            icon?: string;
        };
    }>;
    /**
     * Props to pass to ava-button when using the 'button' variant. All ava-button @Inputs are supported.
     * Defaults to { variant: 'secondary' } for design system consistency.
     */
    buttonProps: Partial<ButtonComponent>;
    /**
     * Style object for the tab list (nav.ava-tabs__list). Useful for glassmorphic, neomorphic, or custom backgrounds.
     */
    listStyle?: Record<string, string>;
    /**
     * Style object for the wrapper (div.awe-tabs__container). Useful for custom backgrounds or effects when container is false.
     */
    wrapperStyle?: Record<string, string>;
    /**
     * Optional style object for the dropdown menu (ava-tabs__dropdown-menu). Allows full customization.
     */
    dropdownMenuStyle?: Record<string, string>;
    get tabsCount(): number;
    tabList: ElementRef<HTMLElement>;
    tabButtons: QueryList<ElementRef<HTMLButtonElement>>;
    underline: {
        width: number;
        left: number;
    };
    showScrollButtons: boolean;
    disableScrollLeft: boolean;
    disableScrollRight: boolean;
    openDropdownIndex: number | null;
    isTabHovered: number | null;
    isDropdownHovered: number | null;
    dropdownPosition: {
        left: number;
        top: number;
    } | null;
    private resizeObserver?;
    private lastTabsSnapshot;
    private lastValue;
    private initialized;
    private dropdownCloseTimeout;
    private tabButtonRefs;
    private dropdownMenuRef;
    private justOpenedDropdown;
    constructor(cdr: ChangeDetectorRef);
    ngOnInit(): void;
    ngAfterViewInit(): void;
    ngAfterViewChecked(): void;
    ngOnDestroy(): void;
    private initializeComponent;
    onTabClick(tab: AvaTab, event?: Event): void;
    onTabHover(tab: AvaTab): void;
    onTabFocus(tab: AvaTab): void;
    onTabBlur(tab: AvaTab): void;
    scroll(direction: 'left' | 'right'): void;
    private checkForOverflow;
    updateScrollButtonState(): void;
    private updateUnderlinePosition;
    get activeTab(): AvaTab | undefined;
    onDropdownItemClick(tab: AvaTab, item: {
        label: string;
        value: string | number;
        icon?: string;
    }): void;
    onDropdownItemHover(tab: AvaTab, item: {
        label: string;
        value: string | number;
        icon?: string;
    }): void;
    onDropdownItemFocus(tab: AvaTab, item: {
        label: string;
        value: string | number;
        icon?: string;
    }): void;
    onDropdownItemBlur(tab: AvaTab, item: {
        label: string;
        value: string | number;
        icon?: string;
    }): void;
    onTabDropdownEnter(i: number, tabButton?: HTMLElement): void;
    onTabDropdownLeave(i: number): void;
    onDropdownMenuEnter(i: number, ref?: HTMLElement): void;
    onDropdownMenuLeave(i: number): void;
    private addDocumentClickListener;
    private removeDocumentClickListener;
    private handleDocumentClick;
    get customStyle(): {
        'max-width'?: string | undefined;
        width?: string | undefined;
    };
    static ɵfac: i0.ɵɵFactoryDeclaration<TabsComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TabsComponent, "ava-tabs", never, { "tabs": { "alias": "tabs"; "required": false; }; "value": { "alias": "value"; "required": false; }; "highlightActiveText": { "alias": "highlightActiveText"; "required": false; }; "maxWidth": { "alias": "maxWidth"; "required": false; }; "showChevrons": { "alias": "showChevrons"; "required": false; }; "ariaLabel": { "alias": "ariaLabel"; "required": false; }; "variant": { "alias": "variant"; "required": false; }; "style": { "alias": "style"; "required": false; }; "iconColor": { "alias": "iconColor"; "required": false; }; "container": { "alias": "container"; "required": false; }; "containerStyle": { "alias": "containerStyle"; "required": false; }; "buttonProps": { "alias": "buttonProps"; "required": false; }; "listStyle": { "alias": "listStyle"; "required": false; }; "wrapperStyle": { "alias": "wrapperStyle"; "required": false; }; "dropdownMenuStyle": { "alias": "dropdownMenuStyle"; "required": false; }; }, { "valueChange": "valueChange"; "dropdownSelect": "dropdownSelect"; "tabClick": "tabClick"; "tabHover": "tabHover"; "tabFocus": "tabFocus"; "tabBlur": "tabBlur"; "dropdownItemClick": "dropdownItemClick"; "dropdownItemHover": "dropdownItemHover"; "dropdownItemFocus": "dropdownItemFocus"; "dropdownItemBlur": "dropdownItemBlur"; }, never, never, true, never>;
}
