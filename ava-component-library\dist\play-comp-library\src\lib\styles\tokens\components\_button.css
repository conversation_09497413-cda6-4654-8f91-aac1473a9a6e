/**
 * =========================================================================
 * Play+ Design System: Button Component Tokens
 *
 * Component-specific semantic tokens for button elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for button styling.
 * =========================================================================
 */

:root {
  /* --- Button Base --- */
  --button-font: var(--font-body-2);
  --button-font-weight: var(--global-font-weight-regular);
  --button-line-height: var(--global-line-height-default);
  --button-border-radius: var(--global-radius-sm);
  --button-transition: var(--motion-pattern-fade);
  --button-cursor: pointer;
  --button-cursor-disabled: not-allowed;
  --button-outline: var(--accessibility-focus-ring-width) solid var(--color-brand-primary);

  /* --- Button Primary --- */
  --button-primary-background: var(--color-brand-primary);
  --button-primary-background-hover: var(--color-brand-primary-hover);
  --button-primary-background-active: var(--color-brand-primary-active);
  --button-primary-background-disabled: var(--color-surface-disabled);
  --button-primary-text: var(--color-text-on-brand);
  --button-primary-text-disabled: var(--color-text-disabled);
  --button-primary-border: none;
  --button-primary-border-hover: none;
  --button-primary-border-active: none;
  --button-primary-border-disabled: none;

  /* --- Button Secondary --- */
  --button-secondary-background: var(--color-background-secondary);
  --button-secondary-background-hover: var(--color-surface-interactive-secondary-hover);
  --button-secondary-background-active: var(--color-surface-interactive-secondary-active);
  --button-secondary-background-disabled: var(--color-surface-disabled);
  --button-secondary-text: var(--color-text-secondary);
  --button-secondary-text-active: var(--color-text-on-secondary);
  --button-secondary-text-disabled: var(--color-text-disabled);
  --button-secondary-border: 1px solid var(--color-border-secondary);
  --button-secondary-border-hover: 1px solid var(--color-border-secondary-hover);
  --button-secondary-border-active: 1px solid var(--color-border-secondary-active);
  --button-secondary-border-disabled: 1px solid var(--color-border-disabled);

  /* --- Button Danger --- */
  --button-danger-background: var(--global-color-red-500);
  --button-danger-background-hover: var(--global-color-red-600);
  --button-danger-background-active: var(--global-color-red-700);
  --button-danger-text: var(--color-text-on-brand);
  --button-danger-border: none;

    /* --- Button warning --- */
  --button-warning-background: var(--global-color-yellow-500);
  --button-warning-background-hover: var(--global-color-yellow-600);
  --button-warning-background-active: var(--global-color-yellow-700);
  --button-warning-text: var(--color-text-on-brand);
  --button-warning-border: none;

  /* --- Button Skeleton --- */
  --button-skeleton-background: var(--color-background-primary);
  --button-skeleton-border: 1px solid var(--color-surface-disabled);
  --button-skeleton-icon-background: linear-gradient(
    90deg,
    var(--color-surface-disabled) 25%,
    var(--color-surface-subtle) 37%,
    var(--color-surface-disabled) 63%
  );
  --button-skeleton-text-background: linear-gradient(
    90deg,
    var(--color-surface-disabled) 25%,
    var(--color-surface-subtle) 37%,
    var(--color-surface-disabled) 63%
  );
  --button-skeleton-animation: skeleton-loading 1.4s ease infinite;
  --button-skeleton-background-size: 400% 100%;

  /* --- Button Sizes --- */
  --button-size-sm-padding: var(--global-spacing-2) var(--global-spacing-3);
  --button-size-sm-font: var(--font-label);
  --button-size-sm-height: 32px;
  --button-size-sm-min-width: 80px;

  --button-size-md-padding: var(--global-spacing-3) var(--global-spacing-4);
  --button-size-md-font: var(--font-body-2);
  --button-size-md-height: 40px;
  --button-size-md-min-width: 100px;

  --button-size-lg-padding: var(--global-spacing-4) var(--global-spacing-5);
  --button-size-lg-font: var(--font-body-1);
  --button-size-lg-height: 48px;
  --button-size-lg-min-width: 120px;

  /* --- Button Icon --- */
  --button-icon-size-sm: 16px;
  --button-icon-size-md: 20px;
  --button-icon-size-lg: 24px;
  --button-icon-margin: var(--global-spacing-2);
  --button-icon-color: inherit;
  --button-icon-color-disabled: var(--color-text-disabled);

  /* --- Button States --- */
  --button-focus-ring: var(--accessibility-focus-ring-width) var(--accessibility-focus-ring-style) var(--accessibility-focus-ring-color);
  --button-focus-ring-offset: var(--accessibility-focus-ring-offset);
  --button-active-transform: scale(0.98);
  --button-hover-transform: scale(1.02);

  /* --- Button Loading --- */
  --button-loading-opacity: 0.7;
  --button-loading-cursor: wait;
  --button-loading-spinner-size: 16px;
  --button-loading-spinner-color: currentColor;
  --button-glassmorphic-light-90: var(--global-glassmorphic-light-90);
  --button-glassmorphic-blur-30 : var(--global-glassmorphic-blur-60);

  /* --- Gradient --- */
  --button-gradient-border: var(--global-color-gray-50);
}
