import * as i0 from "@angular/core";
export interface TableColumn {
    key: string;
    label: string;
    type: string;
    bgColor?: string;
    textColor?: string;
}
export interface TableAction {
    icon: string;
}
export declare class TableComponent {
    columns: TableColumn[];
    data: any[];
    getValue(row: any, key: string): any;
    handleAction(action: string, row: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TableComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TableComponent, "ava-table", never, { "columns": { "alias": "columns"; "required": false; }; "data": { "alias": "data"; "required": false; }; }, {}, never, never, true, never>;
}
