import { TemplateRef } from '@angular/core';
import * as i0 from "@angular/core";
export interface CardData {
    header?: {
        title: string;
        iconName: string;
        viewAll: boolean;
    };
    contents?: [
        {
            session1: any;
        }
    ];
    footer?: {};
}
export declare class ApprovalCardComponent {
    cardData: any;
    height: string;
    contentTemplate: TemplateRef<any>;
    contentsBackground: string;
    cardContainerBackground: string;
    static ɵfac: i0.ɵɵFactoryDeclaration<ApprovalCardComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ApprovalCardComponent, "ava-approval-card", never, { "cardData": { "alias": "cardData"; "required": false; }; "height": { "alias": "height"; "required": false; }; "contentTemplate": { "alias": "contentTemplate"; "required": false; }; "contentsBackground": { "alias": "contentsBackground"; "required": false; }; "cardContainerBackground": { "alias": "cardContainerBackground"; "required": false; }; }, {}, never, ["div[contentFooter]"], true, never>;
}
