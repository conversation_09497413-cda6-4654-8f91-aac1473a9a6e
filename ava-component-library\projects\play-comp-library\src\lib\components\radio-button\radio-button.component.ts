import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';

export interface RadioOption {
  label: string;
  value: string;
  disabled?: boolean;
}

@Component({
  selector: 'ava-radio-button',
  imports: [CommonModule],
  templateUrl: './radio-button.component.html',
  styleUrl: './radio-button.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RadioButtonComponent),
      multi: true
    }
  ]
})
export class RadioButtonComponent implements ControlValueAccessor {
  @Input() options: RadioOption[] = [];
  @Input() name: string = '';
  @Input() selectedValue: string = '';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() orientation: 'horizontal' | 'vertical' = 'vertical';
  @Input() color: string = '';
  @Input() labelColor: string = '';
  @Input() radio: 'dot' | 'none' = 'dot';
  @Input() animation: 'none' | 'shadow' = 'none';

  hovered: string | null = null;
  @Output() selectedValueChange = new EventEmitter<string>();

  private onChange: (value: string) => void = () => { };
  private onTouched: () => void = () => { };
  isDisabled = false;

  onSelectionChange(value: string) {
    if (this.isDisabled) return;
    this.selectedValue = value;
    this.selectedValueChange.emit(value);
    this.onChange(value);
    this.onTouched();
  }

  // ControlValueAccessor methods
  writeValue(value: string): void {
    this.selectedValue = value;
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }
}

