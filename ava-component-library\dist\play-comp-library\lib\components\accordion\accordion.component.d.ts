import * as i0 from "@angular/core";
export declare class AccordionComponent {
    expanded: boolean;
    animation: boolean;
    controlled: boolean;
    iconClosed: string;
    iconOpen: string;
    titleIcon: string;
    iconPosition: 'left' | 'right';
    type: 'default' | 'titleIcon';
    get accordionClasses(): {
        animated: boolean;
        expanded: boolean;
    };
    onAccordionKeydown(event: KeyboardEvent): void;
    toggleExpand(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<AccordionComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<AccordionComponent, "ava-accordion", never, { "expanded": { "alias": "expanded"; "required": false; }; "animation": { "alias": "animation"; "required": false; }; "controlled": { "alias": "controlled"; "required": false; }; "iconClosed": { "alias": "iconClosed"; "required": false; }; "iconOpen": { "alias": "iconOpen"; "required": false; }; "titleIcon": { "alias": "titleIcon"; "required": false; }; "iconPosition": { "alias": "iconPosition"; "required": false; }; "type": { "alias": "type"; "required": false; }; }, {}, never, ["[header]", "[content]"], true, never>;
}
