import * as i0 from "@angular/core";
export declare class ImageCardComponent {
    imageUrl: string;
    name: string;
    title: string;
    imagePosition: 'left' | 'right' | 'top' | 'bottom';
    getWrapperClass(): "img-card-wrapper-vertical" | "img-card-wrapper-horizontal";
    static ɵfac: i0.ɵɵFactoryDeclaration<ImageCardComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<ImageCardComponent, "ava-image-card", never, { "imageUrl": { "alias": "imageUrl"; "required": false; }; "name": { "alias": "name"; "required": false; }; "title": { "alias": "title"; "required": false; }; "imagePosition": { "alias": "imagePosition"; "required": false; }; }, {}, never, never, true, never>;
}
