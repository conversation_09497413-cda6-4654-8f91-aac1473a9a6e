import { ChangeDetectionStrategy, Component, ViewEncapsulation, Input, OnInit, inject, ChangeDetectorRef } from '@angular/core';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { IconComponent, SidebarComponent } from '../../../public-api';
import { SidebarItem } from '../../../../../playground/src/app/components/app-sidebar/app-sidebar.component';
import { CommonModule } from '@angular/common';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

@Component({
  selector: 'ava-custom-sidebar',
  imports: [SidebarComponent, IconComponent, CommonModule,HttpClientModule],
  templateUrl: './custom-sidebar.component.html',
  styleUrl: './custom-sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class CustomSidebarComponent implements OnInit {
  @Input() jsonFilePath: string | null = null;
  @Input() sidebarItemsData: SidebarItem[] | null = null;
  
  private http = inject(HttpClient);
  private cdr = inject(ChangeDetectorRef);
  
  isCollapsed = false;
  sidebarItems: SidebarItem[] = [];
  isLoading = false;
  loadError: string | null = null;

  private defaultSidebarItems: SidebarItem[] = [
    {
      id: '1',
      icon: 'home',
      text: 'Dashboard',
      route: '/dashboard',
      active: true,
    },
    { id: '2', icon: 'users', text: 'Users', route: '/users' },
    { id: '3', icon: 'settings', text: 'Settings', route: '/settings' },
    { id: '4', icon: 'analytics', text: 'Analytics', route: '/analytics' },
    { id: '5', icon: 'messages', text: 'Messages', route: '/messages' },
    { id: '6', icon: 'calendar', text: 'Calendar', route: '/calendar' },
  ];

  ngOnInit(): void {
    this.initializeSidebarItems();
  }

  private initializeSidebarItems(): void {
    // Priority: sidebarItemsData > jsonFilePath > defaultSidebarItems
    if (this.sidebarItemsData && this.sidebarItemsData.length > 0) {
      this.sidebarItems = this.sidebarItemsData;
    } else if (this.jsonFilePath) {
      this.loadFromJsonFile();
    } else {
      this.sidebarItems = this.defaultSidebarItems;
    }
  }

  private loadFromJsonFile(): void {
    if (!this.jsonFilePath) return;

    this.isLoading = true;
    this.loadError = null;

    this.loadJsonFile(this.jsonFilePath).subscribe({
      next: (items) => {
        this.sidebarItems = items;
        this.isLoading = false;
        this.cdr.detectChanges(); // Trigger change detection
        console.log('Sidebar items loaded from JSON file:', items);
      },
      error: (error) => {
        this.loadError = `Failed to load sidebar items: ${error}`;
        this.sidebarItems = this.defaultSidebarItems;
        this.isLoading = false;
        this.cdr.detectChanges(); // Trigger change detection
        console.error('Error loading sidebar items from JSON file:', error);
      }
    });
  }

  private loadJsonFile(filePath: string): Observable<SidebarItem[]> {
    return this.http.get<SidebarItem[]>(filePath).pipe(
      tap(items => {
        if (!this.isValidSidebarItems(items)) {
          throw new Error('Invalid sidebar items structure in JSON file');
        }
      }),
      catchError(error => {
        console.error('HTTP Error loading JSON file:', error);
        return of(this.defaultSidebarItems);
      })
    );
  }

  private isValidSidebarItems(items: any): boolean {
    if (!Array.isArray(items)) return false;
    
    return items.every(item => 
      typeof item === 'object' &&
      item !== null &&
      typeof item.id === 'string' &&
      typeof item.icon === 'string' &&
      typeof item.text === 'string' &&
      (typeof item.route === 'string' || item.route === undefined) &&
      (typeof item.active === 'boolean' || item.active === undefined)
    );
  }

  onItemClick(item: SidebarItem): void {
    // Update active state - set all to false, then set clicked item to true
    this.sidebarItems.forEach(sidebarItem => {
      sidebarItem.active = sidebarItem.id === item.id;
    });
    
    this.cdr.detectChanges(); // Trigger change detection for active state
    console.log('Item clicked:', item);
  }

  onCollapseToggle(isCollapsed: boolean): void {
    this.isCollapsed = isCollapsed;
    console.log('Sidebar collapsed:', isCollapsed);
  }

  // Method to load new JSON file programmatically
  loadNewJsonFile(filePath: string): void {
    this.jsonFilePath = filePath;
    this.loadFromJsonFile();
  }

  // Method to update sidebar items programmatically
  updateSidebarItems(newItems: SidebarItem[]): void {
    this.sidebarItemsData = newItems;
    this.jsonFilePath = null; // Clear file path when setting data directly
    this.sidebarItems = newItems;
  }

  // Method to reload current JSON file
  reloadJsonFile(): void {
    if (this.jsonFilePath) {
      this.loadFromJsonFile();
    }
  }
}